#include "soft_i2c.h"
#include "nchd12.h"

uint16_t pcf8575_read_bit12(uint8_t slave_num)
{ 
	uint8_t	hdata,ldata;
	uint16_t bit12;
	_bsp_analog_i2c_start();
	_bsp_analog_i2c_send_byte_nask(slave_num | HOST_READ_COMMAND);
	uint8_t ack=_bsp_analog_i2c_wait_ack();
	ldata=_bsp_analog_i2c_read_byte();
	_bsp_analog_i2c_ack();
	hdata=_bsp_analog_i2c_read_byte();
	_bsp_analog_i2c_nack();
	_bsp_analog_i2c_stop();
	bit12 = (uint16_t)(hdata<<8 | ldata)&0x0fff;
	return bit12;
}

uint16_t pca9555_read_bit12(uint8_t slave_num)
{ 
	uint8_t	hdata,ldata;
	uint16_t bit12;
	_bsp_analog_i2c_start();
	_bsp_analog_i2c_send_byte_nask(slave_num);			//	写从机地址
	_bsp_analog_i2c_wait_ack();
	_bsp_analog_i2c_send_byte_nask(INPUT_PORT_REGISTER0); //写入要读取的寄存器地址
	_bsp_analog_i2c_wait_ack();
	_bsp_analog_i2c_start(); /* 重新开始信号 */
	_bsp_analog_i2c_send_byte_nask(slave_num  | HOST_READ_COMMAND);			   //访问从机地址，并设为读取
	_bsp_analog_i2c_wait_ack();
	ldata=_bsp_analog_i2c_read_byte();
	_bsp_analog_i2c_ack();
	hdata=_bsp_analog_i2c_read_byte();
	_bsp_analog_i2c_nack();
	_bsp_analog_i2c_stop();
	bit12 = (uint16_t)(hdata<<8 | ldata)&0x0fff;
	return bit12;
}

/**
 * @brief PCA9555配置为输入模式
 * @param none
 * @return none
 */
void pca9555_config_input(void)
{
	// 配置所有引脚为输入模式
	_bsp_analog_i2c_start();
	_bsp_analog_i2c_send_byte_nask(0x40);  // 写地址
	_bsp_analog_i2c_wait_ack();
	_bsp_analog_i2c_send_byte_nask(CONFIG_PORT_REGISTER0);  // 配置寄存器0
	_bsp_analog_i2c_wait_ack();
	_bsp_analog_i2c_send_byte_nask(0xFF);  // 全部设为输入
	_bsp_analog_i2c_wait_ack();
	_bsp_analog_i2c_send_byte_nask(0x0F);  // 高4位设为输入
	_bsp_analog_i2c_wait_ack();
	_bsp_analog_i2c_stop();
}
