#include "encoder_driver.h"
#define ENCODER_NUM (2)     //编码器数量
//编码器原始计数值
int16_t encoder_get_count[ENCODER_NUM];//left:0 right:1

encoder encoder_left;//B6-E1B,B7-E1A
encoder encoder_right;//B8-E2A,B9-E2B,
/**
 * @brief 编码器计数值处理函数
 * 
 */
static void encoder_func(void)
{
    //如果是左轮A相触发
    if(DL_GPIO_getEnabledInterruptStatus(ENCODER_PORT,ENCODER_left_a_PIN))
    {
        //判断B相当前状态,执行相应逻辑
        if(DL_GPIO_readPins(ENCODER_PORT,ENCODER_left_b_PIN) == 0)
        {
            //编码器计数值+1
            encoder_get_count[0]++;
        }
        else
        {
            //编码器计数值-1
            encoder_get_count[0]--;
        }
        //清除A相中断状态
        DL_GPIO_clearInterruptStatus(ENCODER_PORT,ENCODER_left_a_PIN);
    }
    //如果是左轮B相触发
    if(DL_GPIO_getEnabledInterruptStatus(ENCODER_PORT,ENCODER_left_b_PIN))
    {
        //判断A相当前状态,执行相应逻辑
        if(DL_GPIO_readPins(ENCODER_PORT,ENCODER_left_a_PIN) == 0)
        {
            encoder_get_count[0]++;
        }
        else
        {
            encoder_get_count[0]--;
        }
        //清除B相中断状态
        DL_GPIO_clearInterruptStatus(ENCODER_PORT,ENCODER_left_b_PIN);
    }
    //如果是右轮A相触发
    if(DL_GPIO_getEnabledInterruptStatus(ENCODER_PORT,ENCODER_right_a_PIN))
    {
        //判断B相当前状态,执行相应逻辑
        if(DL_GPIO_readPins(ENCODER_PORT,ENCODER_right_b_PIN) == 0)
        {
            //编码器计数值+1
            encoder_get_count[1]++;
        }
        else
        {
            //编码器计数值-1
            encoder_get_count[1]--;
        }
        //清除A相中断状态
        DL_GPIO_clearInterruptStatus(ENCODER_PORT,ENCODER_right_a_PIN);
    }
    //如果是右轮B相触发
    if(DL_GPIO_getEnabledInterruptStatus(ENCODER_PORT,ENCODER_right_b_PIN))
    {
        //判断A相当前状态,执行相应逻辑
        if(DL_GPIO_readPins(ENCODER_PORT,ENCODER_right_a_PIN) == 0)
        {
            encoder_get_count[1]++;
        }
        else
        {
            encoder_get_count[1]--;
        }
        //清除B相中断状态
        DL_GPIO_clearInterruptStatus(ENCODER_PORT,ENCODER_right_b_PIN);
    }
}
/**
 * @brief 外部中断处理函数
 * 
 */
void GROUP1_IRQHandler(void)
{
    //判断中断源调用相关处理
    switch( DL_Interrupt_getPendingGroup(DL_INTERRUPT_GROUP_1) )
    {
        //编码器中断源
        case ENCODER_INT_IIDX:
            encoder_func();
        break;
    }
}
/**
 * @brief 编码器初始化
 * 
 * @param encoder 
 * @param id 
 * @param reverse 
 */
void encoder_init(encoder *encoder,uint8_t id,uint8_t reverse)
{
    encoder->id = id;
    encoder->reverse = reverse;
    
    encoder->count = 0;
    encoder->speed_cm_s = 0;
    int32_t total_count = 0;
    float speed_cm_s = 0;
}
/**
 * @brief 编码器数据更新 周期性调用
 * 
 * @param encoder 
 */
void encoder_update(encoder *encoder)
{
    //读取原始计数值
    encoder->count = encoder_get_count[encoder->id];
    //处理编码器反向
    encoder->count = (encoder->reverse == 0 ? encoder->count : -encoder->count);
    //清零计数变量
    encoder_get_count[encoder->id] = 0;
		//my_printf(UART_0_INST,"count:%d\r\n",encoder->count);
    //累计计数
    encoder->total_count += encoder->count;
    //计算速度(cm/s)
    //(计数值/PRR) = 转动圈数
    //转动圈数*周长 = 转动距离
    //转动距离/时间 = 速度
    encoder->speed_cm_s = (float)encoder->count / ENCODER_PRR * WHEEL_C / SAMPLE_TIME_S;
}
/**
 * @brief 编码器任务函数
 * 
 */
__WEAK void encoder_task(void)
{
    encoder_update(&encoder_left);
    encoder_update(&encoder_right);
//    my_printf(UART_0_INST,"encoder_left:total:%d speed:%.2f\r\n",encoder_left.total_count,encoder_left.speed_cm_s);
//    my_printf(UART_0_INST,"encoder_right:total:%d speed:%.2f\r\n",encoder_right.total_count,encoder_right.speed_cm_s);
}
/**
 * @brief 编码器配置
 * 
 */
void encoder_config(void)
{
    encoder_init(&encoder_left,0,0);
    encoder_init(&encoder_right,1,0);
}