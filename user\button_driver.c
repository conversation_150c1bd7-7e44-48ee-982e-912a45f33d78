#include "button_driver.h"

//按键判断变量
unsigned char led_state;
uint8_t key_val,key_old,key_down,key_up;
//按键状态列表
typedef enum{
    KEY_DEBUG = 1
}KEY_STATE;

/**
 * @brief 按键状态读取函数
 * @param none
 * @return uint8_t 
 */
KEY_STATE key_read(void)
{
    uint8_t temp = 0;
    if(DL_GPIO_readPins(KEY_PORT, KEY_PIN_21_PIN) == 0) 
    temp = KEY_DEBUG;
		
		return temp;
}
/**
 * @brief 按键任务处理函数
 * @param none
 * @return __WEAK 
 */
__WEAK void key_task(void)
{
	
    key_val = key_read();
    key_down = key_val & (key_old ^ key_val);
    key_up = ~key_val & (key_old ^ key_val);
    key_old = key_val;

    switch(key_down)
    {
        case KEY_DEBUG:
			led_state=(led_state+1)%2;
//		Motor_SetSpeed(&Motor_l,30);
//		Motor_SetSpeed(&Motor_r,30);
		if(pid_running)
		{
			pid_running=0;
			Motor_Stop();
		}
		else
			pid_running=1;
		if(led_state)
			DL_GPIO_clearPins(LED1_PORT,LED1_PIN_22_PIN);
		else
			DL_GPIO_setPins(LED1_PORT,LED1_PIN_22_PIN);
            my_printf(UART_0_INST,"key test suc\r\n");
        break;
    }
}