#include "soft_i2c.h"
#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

// 直接定义I2C引脚宏 - 根据ti_msp_dl_config.h中的定义
// I2C_PINS_PORT = GPIOA, I2C_PINS_SDA_PIN = DL_GPIO_PIN_1, I2C_PINS_SCL_PIN = DL_GPIO_PIN_0

// GPIO寄存器结构体定义
typedef struct {
    volatile uint32_t DIN31_0;      // 输入寄存器
    volatile uint32_t DOUT31_0;     // 输出寄存器
    volatile uint32_t DOUTSET31_0;  // 输出设置寄存器
    volatile uint32_t DOUTCLR31_0;  // 输出清除寄存器
    // 其他寄存器...
} GPIO_Regs;

#define GPIOA                   ((GPIO_Regs*)0x400A0000UL)  // GPIOA基地址
#define DL_GPIO_PIN_31           (0x00000001U)               // GPIO PIN 0
#define DL_GPIO_PIN_28           (0x00000002U)               // GPIO PIN 1
#define IOMUX_PINCM1            (0x40428004UL)              // SCL IOMUX
#define IOMUX_PINCM2            (0x40428008UL)              // SDA IOMUX

// I2C引脚宏定义映射
#define I2C_SDA_PORT    GPIOA
#define I2C_SDA_PIN     DL_GPIO_PIN_28
#define I2C_SCL_PORT    GPIOA
#define I2C_SCL_PIN     DL_GPIO_PIN_31
#define I2C_SDA_IOMUX   IOMUX_PINCM2
#define I2C_SCL_IOMUX   IOMUX_PINCM1

// GPIO函数参数定义
#define DL_GPIO_INVERSION_DISABLE   0
#define DL_GPIO_RESISTOR_PULL_UP    1
#define DL_GPIO_HYSTERESIS_DISABLE  0
#define DL_GPIO_WAKEUP_DISABLE      0

// GPIO函数实现 - 直接操作寄存器
void DL_GPIO_setPins(void* port, uint32_t pins) {
    GPIO_Regs* gpio = (GPIO_Regs*)port;
    gpio->DOUTSET31_0 = pins; // 设置输出高电平
}

void DL_GPIO_clearPins(void* port, uint32_t pins) {
    GPIO_Regs* gpio = (GPIO_Regs*)port;
    gpio->DOUTCLR31_0 = pins; // 设置输出低电平
}

void DL_GPIO_enableOutput(void* port, uint32_t pins) {
    // 简化实现：在实际硬件中需要配置方向寄存器
    // 这里假设引脚已经配置为输出模式
    (void)port; (void)pins; // 避免编译器警告
}

void DL_GPIO_initDigitalOutput(uint32_t iomux) {
    // 简化实现：配置IOMUX为数字输出
    volatile uint32_t* iomux_reg = (volatile uint32_t*)(uintptr_t)iomux;
    *iomux_reg = 0x00000001; // 基本数字输出配置
}

void DL_GPIO_initDigitalInputFeatures(uint32_t iomux, uint32_t inversion, uint32_t resistor, uint32_t hysteresis, uint32_t wakeup) {
    // 简化实现：配置IOMUX为数字输入
    volatile uint32_t* iomux_reg = (volatile uint32_t*)(uintptr_t)iomux;
    uint32_t config = 0x00000000; // 基本数字输入配置
    if (resistor == DL_GPIO_RESISTOR_PULL_UP) {
        config |= 0x00000002; // 使能上拉
    }
    *iomux_reg = config;
    (void)inversion; (void)hysteresis; (void)wakeup; // 避免编译器警告
}

// I2C引脚操作宏定义
#define _i2c_read_sda()        ((I2C_SDA_PORT->DIN31_0 & I2C_SDA_PIN )>0 ? 0x01 : 0x00)
#define _i2c_sda_high()   		DL_GPIO_setPins(I2C_SDA_PORT,   I2C_SDA_PIN)
#define _i2c_sda_low()  			DL_GPIO_clearPins(I2C_SDA_PORT, I2C_SDA_PIN)
#define _i2c_scl_high()   		DL_GPIO_setPins(I2C_SCL_PORT,   I2C_SCL_PIN)
#define _i2c_scl_low()  			DL_GPIO_clearPins(I2C_SCL_PORT, I2C_SCL_PIN)

/**********************************************************************************************
 *函    名：static void I2C_GPIO_Configuration(void)
 *
 *功    能：无
 *
 *返 回 值：无 
 *
 *备    注：I2C IO初始化
 *********************************************************************************************/
static void I2C_GPIO_Configuration(void)
{	
	DL_GPIO_setPins(I2C_SDA_PORT,   I2C_SDA_PIN);
	DL_GPIO_setPins(I2C_SCL_PORT,   I2C_SCL_PIN);
  _bsp_analog_i2c_stop();
}

void _i2c_sda_out(void)
{
	DL_GPIO_initDigitalOutput(I2C_SDA_IOMUX);
	DL_GPIO_enableOutput(I2C_SDA_PORT, I2C_SDA_PIN);
}

void _i2c_sda_in(void)
{
	//DL_GPIO_initDigitalInput(I2C_SDA_IOMUX);
	DL_GPIO_initDigitalInputFeatures(I2C_SDA_IOMUX,DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_PULL_UP,DL_GPIO_HYSTERESIS_DISABLE, DL_GPIO_WAKEUP_DISABLE);
}

static void _analog_i2c_delay()
{
	uint8_t i;

	/*
	 	软件延时，通过逻辑分析仪测试得到的。
		CPU主频72MHz时，内部Flash运行, MDK编译不优化
		循环次数为10时，SCL频率 = 205KHz 
		循环次数为7时，SCL频率 = 347KHz， SCL高电平时间1.5us，SCL低电平时间2.87us 
	 	循环次数为5时，SCL频率 = 421KHz， SCL高电平时间1.25us，SCL低电平时间2.375us 
        
    IAR编译编译效率高，循环次数为7
	*/
	for (i = 0; i < 10; i++);
}

void _bsp_analog_i2c_start(void)
{
    /*    _____
     *SDA      \_____________
     *    __________
     *SCL           \________
     */
		_i2c_sda_out();
    _i2c_sda_high();
	  _analog_i2c_delay();
    _i2c_scl_high();
    _analog_i2c_delay();
    _i2c_sda_low();
    _analog_i2c_delay();
    _i2c_scl_low();
    _analog_i2c_delay();
}

void _bsp_analog_i2c_stop(void)
{
    /*               _______
     *SDA __________/
     *          ____________
     *SCL _____/
     */
		_i2c_sda_out();
		_i2c_scl_low();//add
    _i2c_sda_low();
    _i2c_scl_high();
    _analog_i2c_delay();
    _i2c_sda_high();
    _analog_i2c_delay();
}

uint8_t _bsp_analog_i2c_wait_ack(void)
{
    uint32_t timeout = 0;
	  _i2c_scl_low();
	  _i2c_sda_high();
		_i2c_sda_in();
    _analog_i2c_delay();
    _i2c_scl_high();
    _analog_i2c_delay();
    while(_i2c_read_sda())
    {
        timeout++;
        if(timeout > 100)//2000
        {
					_bsp_analog_i2c_stop();
          return 1;
        }
    }
    _i2c_scl_low();
    _analog_i2c_delay();
    return 0;
}

void _bsp_analog_i2c_ack(void)
{
	/*           ____
	 *SCL ______/    \______
	 *    ____         _____
	 *SDA     \_______/
	 */
	_i2c_sda_out();
	_i2c_scl_low();
	_i2c_sda_high();
	_analog_i2c_delay();
	_i2c_sda_low();
	_analog_i2c_delay();
	_i2c_scl_high();
	_analog_i2c_delay();
	_i2c_scl_low();
	_analog_i2c_delay();
	_i2c_sda_high();
}

void _bsp_analog_i2c_nack(void)
{
    /*           ____
     *SCL ______/    \______
     *    __________________
     *SDA
     */
	  _i2c_scl_low();
		_i2c_sda_out();
    _i2c_sda_high();
    _analog_i2c_delay();
    _i2c_scl_high();
    _analog_i2c_delay();
    _i2c_scl_low();
    _analog_i2c_delay();
}

void _bsp_analog_i2c_send_byte(uint8_t data)
{
	uint8_t i;
	_i2c_sda_out();
	_i2c_scl_low();
	for(i = 0; i < 8; i++)
	{
		if((data & 0x80) >> 7)	_i2c_sda_high();
		else	_i2c_sda_low();
		data <<= 1;
		_analog_i2c_delay();
		_i2c_scl_high();
		_analog_i2c_delay();
		_i2c_scl_low();
	}
	_bsp_analog_i2c_wait_ack();
}

void _bsp_analog_i2c_send_byte_nask(uint8_t data)
{
	uint8_t i;
	_i2c_sda_out();
	_i2c_scl_low();
	for(i = 0; i < 8; i++)
	{
		if((data & 0x80) >> 7)	_i2c_sda_high();
		else	_i2c_sda_low();
		data <<= 1;
		_analog_i2c_delay();
		_i2c_scl_high();
		_analog_i2c_delay();
		_i2c_scl_low();
	}
}

uint8_t _bsp_analog_i2c_read_byte(void)
{
		_i2c_sda_high();
		_i2c_sda_in();
    uint8_t i, data = 0;
    for(i = 0; i < 8; i++ )
    {
        data <<= 1;
        _i2c_scl_high();
				_analog_i2c_delay();
        if(_i2c_read_sda())
        {
            data++;
        }
        _i2c_scl_low();
        _analog_i2c_delay();
    }
    return data;
}

uint8_t i2c_CheckDevice(uint8_t _Address)
{
	uint8_t ucAck;
	I2C_GPIO_Configuration();	
	_bsp_analog_i2c_start();		/* 发送启动信号 */
	/* 发送设备地址+读写控制bit（0 = w， 1 = r) bit7 先传 */
	_bsp_analog_i2c_send_byte_nask(_Address | I2C_WR);
	ucAck = _bsp_analog_i2c_wait_ack();	/* 检测设备的ACK应答 */
	_bsp_analog_i2c_stop();			/* 发送停止信号 */
	return ucAck;
}
