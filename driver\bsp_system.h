#ifndef __BSP_SYSTEM_H
#define __BSP_SYSTEM_H

#include "stdio.h"
#include "stdarg.h"
#include "string.h"

#include "ti_msp_dl_config.h"
#include "uart_driver.h"
#include "button_driver.h"
#include "iic_driver.h"
#include "encoder_driver.h"
#include "motor_driver.h"
#include "pid.h"
//#include "bno08x_hal.h"
//#include "IIC.h"
#include "Time.h"
//#include "hardware_iic.h"
#include "gray.h"
#include "soft_i2c.h"
#include "nchd12.h"

#include "scheduler.h"
#include "motor.h"
#include "pid_app.h"

extern uint32_t uwTick;    //系统时间
extern bool pid_running; // PID 控制使能开关
extern PID_T pid_line_gray;
extern int basic_speed;
extern PID_T pid_speed_left;  // 左轮速度环
extern PID_T pid_speed_right; // 右轮速度环
#endif

