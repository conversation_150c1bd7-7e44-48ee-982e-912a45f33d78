Component: Arm Compiler for Embedded 6.23 Tool: armlink [5f102400]

==============================================================================

Section Cross References

    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to scheduler.o(.text.SysTick_Handler) for SysTick_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to encoder_driver.o(.text.GROUP1_IRQHandler) for GROUP1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to uart_driver.o(.text.UART1_IRQHandler) for UART1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to uart_driver.o(.text.UART0_IRQHandler) for UART0_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) for SYSCFG_DL_MOTOR_PWM_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for SYSCFG_DL_UART_1_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init) for SYSCFG_DL_SYSCTL_CLK_init
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for DL_SYSCTL_setHFCLKSourceHFXTParams
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for DL_SYSCTL_configSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.rodata.gSYSPLLConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to ti_msp_dl_config.o(.rodata.gMOTOR_PWMClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to ti_msp_dl_config.o(.rodata.gMOTOR_PWMConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_MOTOR_PWM_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_CLK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init) for [Anonymous Symbol]
    main.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    main.o(.text.main) refers to uart_driver.o(.text.my_printf) for my_printf
    main.o(.text.main) refers to soft_i2c.o(.text.i2c_CheckDevice) for i2c_CheckDevice
    main.o(.text.main) refers to nchd12.o(.text.pca9555_config_input) for pca9555_config_input
    main.o(.text.main) refers to encoder_driver.o(.text.encoder_config) for encoder_config
    main.o(.text.main) refers to pid_app.o(.text.PID_Init) for PID_Init
    main.o(.text.main) refers to scheduler.o(.text.scheduler_init) for scheduler_init
    main.o(.text.main) refers to scheduler.o(.text.scheduler_run) for scheduler_run
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.user_config) refers to soft_i2c.o(.text.i2c_CheckDevice) for i2c_CheckDevice
    main.o(.text.user_config) refers to nchd12.o(.text.pca9555_config_input) for pca9555_config_input
    main.o(.ARM.exidx.text.user_config) refers to main.o(.text.user_config) for [Anonymous Symbol]
    uart_driver.o(.ARM.exidx.text.uart_send_char) refers to uart_driver.o(.text.uart_send_char) for [Anonymous Symbol]
    uart_driver.o(.ARM.exidx.text.uart_send_string) refers to uart_driver.o(.text.uart_send_string) for [Anonymous Symbol]
    uart_driver.o(.text.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart_driver.o(.ARM.exidx.text.my_printf) refers to uart_driver.o(.text.my_printf) for [Anonymous Symbol]
    uart_driver.o(.text.UART0_IRQHandler) refers to uart_driver.o(.bss.uart_rx_index) for uart_rx_index
    uart_driver.o(.text.UART0_IRQHandler) refers to uart_driver.o(.bss.uart_rx_buffer) for uart_rx_buffer
    uart_driver.o(.text.UART0_IRQHandler) refers to scheduler.o(.bss.uwTick) for uwTick
    uart_driver.o(.text.UART0_IRQHandler) refers to uart_driver.o(.bss.uart_tick) for uart_tick
    uart_driver.o(.ARM.exidx.text.UART0_IRQHandler) refers to uart_driver.o(.text.UART0_IRQHandler) for [Anonymous Symbol]
    uart_driver.o(.text.UART1_IRQHandler) refers to uart_driver.o(.bss.uart_rx_index_1) for uart_rx_index_1
    uart_driver.o(.text.UART1_IRQHandler) refers to uart_driver.o(.bss.uart_rx_buffer_1) for uart_rx_buffer_1
    uart_driver.o(.text.UART1_IRQHandler) refers to scheduler.o(.bss.uwTick) for uwTick
    uart_driver.o(.text.UART1_IRQHandler) refers to uart_driver.o(.bss.uart_tick_1) for uart_tick_1
    uart_driver.o(.ARM.exidx.text.UART1_IRQHandler) refers to uart_driver.o(.text.UART1_IRQHandler) for [Anonymous Symbol]
    uart_driver.o(.text.uart0_task) refers to uart_driver.o(.text.my_printf) for my_printf
    uart_driver.o(.text.uart0_task) refers to memseta.o(.text) for __aeabi_memclr4
    uart_driver.o(.text.uart0_task) refers to uart_driver.o(.bss.uart_rx_index) for uart_rx_index
    uart_driver.o(.text.uart0_task) refers to uart_driver.o(.bss.uart_tick) for uart_tick
    uart_driver.o(.text.uart0_task) refers to scheduler.o(.bss.uwTick) for uwTick
    uart_driver.o(.text.uart0_task) refers to uart_driver.o(.rodata.str1.1) for [Anonymous Symbol]
    uart_driver.o(.text.uart0_task) refers to uart_driver.o(.bss.uart_rx_buffer) for uart_rx_buffer
    uart_driver.o(.ARM.exidx.text.uart0_task) refers to uart_driver.o(.text.uart0_task) for [Anonymous Symbol]
    uart_driver.o(.text.uart1_task) refers to uart_driver.o(.text.my_printf) for my_printf
    uart_driver.o(.text.uart1_task) refers to memseta.o(.text) for __aeabi_memclr4
    uart_driver.o(.text.uart1_task) refers to uart_driver.o(.bss.uart_rx_index_1) for uart_rx_index_1
    uart_driver.o(.text.uart1_task) refers to uart_driver.o(.bss.uart_tick_1) for uart_tick_1
    uart_driver.o(.text.uart1_task) refers to scheduler.o(.bss.uwTick) for uwTick
    uart_driver.o(.text.uart1_task) refers to uart_driver.o(.rodata.str1.1) for [Anonymous Symbol]
    uart_driver.o(.text.uart1_task) refers to uart_driver.o(.bss.uart_rx_buffer_1) for uart_rx_buffer_1
    uart_driver.o(.ARM.exidx.text.uart1_task) refers to uart_driver.o(.text.uart1_task) for [Anonymous Symbol]
    button_driver.o(.ARM.exidx.text.key_read) refers to button_driver.o(.text.key_read) for [Anonymous Symbol]
    button_driver.o(.text.key_task) refers to motor_driver.o(.text.Motor_Stop) for Motor_Stop
    button_driver.o(.text.key_task) refers to uart_driver.o(.text.my_printf) for my_printf
    button_driver.o(.text.key_task) refers to button_driver.o(.bss.key_val) for key_val
    button_driver.o(.text.key_task) refers to button_driver.o(.bss.key_old) for key_old
    button_driver.o(.text.key_task) refers to button_driver.o(.bss.key_down) for key_down
    button_driver.o(.text.key_task) refers to button_driver.o(.bss.key_up) for key_up
    button_driver.o(.text.key_task) refers to button_driver.o(.bss.led_state) for led_state
    button_driver.o(.text.key_task) refers to pid_app.o(.bss.pid_running) for pid_running
    button_driver.o(.ARM.exidx.text.key_task) refers to button_driver.o(.text.key_task) for [Anonymous Symbol]
    encoder_driver.o(.text.GROUP1_IRQHandler) refers to encoder_driver.o(.bss.encoder_get_count) for encoder_get_count
    encoder_driver.o(.ARM.exidx.text.GROUP1_IRQHandler) refers to encoder_driver.o(.text.GROUP1_IRQHandler) for [Anonymous Symbol]
    encoder_driver.o(.ARM.exidx.text.encoder_init) refers to encoder_driver.o(.text.encoder_init) for [Anonymous Symbol]
    encoder_driver.o(.text.encoder_update) refers to fflti.o(.text) for __aeabi_i2f
    encoder_driver.o(.text.encoder_update) refers to fdiv.o(.text) for __aeabi_fdiv
    encoder_driver.o(.text.encoder_update) refers to fmul.o(.text) for __aeabi_fmul
    encoder_driver.o(.text.encoder_update) refers to encoder_driver.o(.bss.encoder_get_count) for encoder_get_count
    encoder_driver.o(.ARM.exidx.text.encoder_update) refers to encoder_driver.o(.text.encoder_update) for [Anonymous Symbol]
    encoder_driver.o(.text.encoder_task) refers to fflti.o(.text) for __aeabi_i2f
    encoder_driver.o(.text.encoder_task) refers to fdiv.o(.text) for __aeabi_fdiv
    encoder_driver.o(.text.encoder_task) refers to fmul.o(.text) for __aeabi_fmul
    encoder_driver.o(.text.encoder_task) refers to encoder_driver.o(.bss.encoder_left) for encoder_left
    encoder_driver.o(.text.encoder_task) refers to encoder_driver.o(.bss.encoder_get_count) for encoder_get_count
    encoder_driver.o(.text.encoder_task) refers to encoder_driver.o(.bss.encoder_right) for encoder_right
    encoder_driver.o(.ARM.exidx.text.encoder_task) refers to encoder_driver.o(.text.encoder_task) for [Anonymous Symbol]
    encoder_driver.o(.text.encoder_config) refers to encoder_driver.o(.bss.encoder_left) for encoder_left
    encoder_driver.o(.text.encoder_config) refers to encoder_driver.o(.bss.encoder_right) for encoder_right
    encoder_driver.o(.ARM.exidx.text.encoder_config) refers to encoder_driver.o(.text.encoder_config) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_Speed_l) refers to fcmpgt.o(.text) for __aeabi_fcmpgt
    motor_driver.o(.text.Motor_Speed_l) refers to fcmpeq.o(.text) for __aeabi_fcmpeq
    motor_driver.o(.text.Motor_Speed_l) refers to fcmpge.o(.text) for __aeabi_fcmpge
    motor_driver.o(.text.Motor_Speed_l) refers to fmul.o(.text) for __aeabi_fmul
    motor_driver.o(.text.Motor_Speed_l) refers to ffixui.o(.text) for __aeabi_f2uiz
    motor_driver.o(.text.Motor_Speed_l) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_driver.o(.ARM.exidx.text.Motor_Speed_l) refers to motor_driver.o(.text.Motor_Speed_l) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_Speed_r) refers to fcmpgt.o(.text) for __aeabi_fcmpgt
    motor_driver.o(.text.Motor_Speed_r) refers to fcmpeq.o(.text) for __aeabi_fcmpeq
    motor_driver.o(.text.Motor_Speed_r) refers to fcmpge.o(.text) for __aeabi_fcmpge
    motor_driver.o(.text.Motor_Speed_r) refers to fmul.o(.text) for __aeabi_fmul
    motor_driver.o(.text.Motor_Speed_r) refers to ffixui.o(.text) for __aeabi_f2uiz
    motor_driver.o(.text.Motor_Speed_r) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_driver.o(.ARM.exidx.text.Motor_Speed_r) refers to motor_driver.o(.text.Motor_Speed_r) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_Stop) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_driver.o(.ARM.exidx.text.Motor_Stop) refers to motor_driver.o(.text.Motor_Stop) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_init) refers to pid.o(.text.pid_init) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_set_target) refers to pid.o(.text.pid_set_target) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_set_params) refers to pid.o(.text.pid_set_params) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_set_limit) refers to pid.o(.text.pid_set_limit) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_reset) refers to pid.o(.text.pid_reset) for [Anonymous Symbol]
    pid.o(.text.pid_calculate_positional) refers to fadd.o(.text) for __aeabi_fsub
    pid.o(.text.pid_calculate_positional) refers to fmul.o(.text) for __aeabi_fmul
    pid.o(.text.pid_calculate_positional) refers to fcmpgt.o(.text) for __aeabi_fcmpgt
    pid.o(.text.pid_calculate_positional) refers to fcmpge.o(.text) for __aeabi_fcmpge
    pid.o(.ARM.exidx.text.pid_calculate_positional) refers to pid.o(.text.pid_calculate_positional) for [Anonymous Symbol]
    pid.o(.text.pid_calculate_incremental) refers to fadd.o(.text) for __aeabi_fsub
    pid.o(.text.pid_calculate_incremental) refers to fmul.o(.text) for __aeabi_fmul
    pid.o(.text.pid_calculate_incremental) refers to fcmpgt.o(.text) for __aeabi_fcmpgt
    pid.o(.text.pid_calculate_incremental) refers to fcmpge.o(.text) for __aeabi_fcmpge
    pid.o(.ARM.exidx.text.pid_calculate_incremental) refers to pid.o(.text.pid_calculate_incremental) for [Anonymous Symbol]
    pid.o(.text.pid_constrain) refers to fcmplt.o(.text) for __aeabi_fcmplt
    pid.o(.text.pid_constrain) refers to fcmpgt.o(.text) for __aeabi_fcmpgt
    pid.o(.ARM.exidx.text.pid_constrain) refers to pid.o(.text.pid_constrain) for [Anonymous Symbol]
    pid.o(.text.pid_app_limit_integral) refers to fcmpgt.o(.text) for __aeabi_fcmpgt
    pid.o(.text.pid_app_limit_integral) refers to fcmpge.o(.text) for __aeabi_fcmpge
    pid.o(.ARM.exidx.text.pid_app_limit_integral) refers to pid.o(.text.pid_app_limit_integral) for [Anonymous Symbol]
    time.o(.ARM.exidx.text.delay_us) refers to time.o(.text.delay_us) for [Anonymous Symbol]
    time.o(.ARM.exidx.text.delay_ms) refers to time.o(.text.delay_ms) for [Anonymous Symbol]
    gray.o(.text.Digtal_Get_12) refers to nchd12.o(.text.pca9555_read_bit12) for pca9555_read_bit12
    gray.o(.text.Digtal_Get_12) refers to gray.o(.bss.Digtal_Get_12.error_count) for [Anonymous Symbol]
    gray.o(.text.Digtal_Get_12) refers to gray.o(.bss.Digtal_Get_12.backup_data) for [Anonymous Symbol]
    gray.o(.ARM.exidx.text.Digtal_Get_12) refers to gray.o(.text.Digtal_Get_12) for [Anonymous Symbol]
    gray.o(.text.Gray_Task) refers to nchd12.o(.text.pca9555_read_bit12) for pca9555_read_bit12
    gray.o(.text.Gray_Task) refers to fadd.o(.text) for __aeabi_fadd
    gray.o(.text.Gray_Task) refers to ffltui.o(.text) for __aeabi_ui2f
    gray.o(.text.Gray_Task) refers to fdiv.o(.text) for __aeabi_fdiv
    gray.o(.text.Gray_Task) refers to motor_driver.o(.text.Motor_Stop) for Motor_Stop
    gray.o(.text.Gray_Task) refers to motor_driver.o(.text.Motor_Speed_l) for Motor_Speed_l
    gray.o(.text.Gray_Task) refers to motor_driver.o(.text.Motor_Speed_r) for Motor_Speed_r
    gray.o(.text.Gray_Task) refers to time.o(.text.delay_ms) for delay_ms
    gray.o(.text.Gray_Task) refers to pid.o(.text.pid_reset) for pid_reset
    gray.o(.text.Gray_Task) refers to fflti.o(.text) for __aeabi_i2f
    gray.o(.text.Gray_Task) refers to pid.o(.text.pid_set_target) for pid_set_target
    gray.o(.text.Gray_Task) refers to gray.o(.bss.Digtal_Get_12.error_count) for [Anonymous Symbol]
    gray.o(.text.Gray_Task) refers to gray.o(.bss.Digtal_Get_12.backup_data) for [Anonymous Symbol]
    gray.o(.text.Gray_Task) refers to gray.o(.bss.Digtal_12) for Digtal_12
    gray.o(.text.Gray_Task) refers to gray.o(.data.gray_weights_12) for gray_weights_12
    gray.o(.text.Gray_Task) refers to gray.o(.bss.g_line_position_error) for g_line_position_error
    gray.o(.text.Gray_Task) refers to pid_app.o(.bss.pid_running) for pid_running
    gray.o(.text.Gray_Task) refers to gray.o(.bss.baochi_flag) for baochi_flag
    gray.o(.text.Gray_Task) refers to scheduler.o(.bss.L_count) for L_count
    gray.o(.text.Gray_Task) refers to pid_app.o(.data.basic_speed) for basic_speed
    gray.o(.text.Gray_Task) refers to pid_app.o(.bss.pid_speed_left) for pid_speed_left
    gray.o(.text.Gray_Task) refers to pid_app.o(.bss.pid_speed_right) for pid_speed_right
    gray.o(.text.Gray_Task) refers to pid_app.o(.bss.pid_line_gray) for pid_line_gray
    gray.o(.ARM.exidx.text.Gray_Task) refers to gray.o(.text.Gray_Task) for [Anonymous Symbol]
    soft_i2c.o(.ARM.exidx.text._i2c_sda_out) refers to soft_i2c.o(.text._i2c_sda_out) for [Anonymous Symbol]
    soft_i2c.o(.ARM.exidx.text._i2c_sda_in) refers to soft_i2c.o(.text._i2c_sda_in) for [Anonymous Symbol]
    soft_i2c.o(.ARM.exidx.text._bsp_analog_i2c_start) refers to soft_i2c.o(.text._bsp_analog_i2c_start) for [Anonymous Symbol]
    soft_i2c.o(.ARM.exidx.text._bsp_analog_i2c_stop) refers to soft_i2c.o(.text._bsp_analog_i2c_stop) for [Anonymous Symbol]
    soft_i2c.o(.ARM.exidx.text._bsp_analog_i2c_wait_ack) refers to soft_i2c.o(.text._bsp_analog_i2c_wait_ack) for [Anonymous Symbol]
    soft_i2c.o(.ARM.exidx.text._bsp_analog_i2c_ack) refers to soft_i2c.o(.text._bsp_analog_i2c_ack) for [Anonymous Symbol]
    soft_i2c.o(.ARM.exidx.text._bsp_analog_i2c_nack) refers to soft_i2c.o(.text._bsp_analog_i2c_nack) for [Anonymous Symbol]
    soft_i2c.o(.text._bsp_analog_i2c_send_byte) refers to soft_i2c.o(.text._bsp_analog_i2c_wait_ack) for _bsp_analog_i2c_wait_ack
    soft_i2c.o(.ARM.exidx.text._bsp_analog_i2c_send_byte) refers to soft_i2c.o(.text._bsp_analog_i2c_send_byte) for [Anonymous Symbol]
    soft_i2c.o(.ARM.exidx.text._bsp_analog_i2c_send_byte_nask) refers to soft_i2c.o(.text._bsp_analog_i2c_send_byte_nask) for [Anonymous Symbol]
    soft_i2c.o(.ARM.exidx.text._bsp_analog_i2c_read_byte) refers to soft_i2c.o(.text._bsp_analog_i2c_read_byte) for [Anonymous Symbol]
    soft_i2c.o(.text.i2c_CheckDevice) refers to soft_i2c.o(.text._bsp_analog_i2c_send_byte_nask) for _bsp_analog_i2c_send_byte_nask
    soft_i2c.o(.text.i2c_CheckDevice) refers to soft_i2c.o(.text._bsp_analog_i2c_wait_ack) for _bsp_analog_i2c_wait_ack
    soft_i2c.o(.ARM.exidx.text.i2c_CheckDevice) refers to soft_i2c.o(.text.i2c_CheckDevice) for [Anonymous Symbol]
    nchd12.o(.text.pcf8575_read_bit12) refers to soft_i2c.o(.text._bsp_analog_i2c_start) for _bsp_analog_i2c_start
    nchd12.o(.text.pcf8575_read_bit12) refers to soft_i2c.o(.text._bsp_analog_i2c_send_byte_nask) for _bsp_analog_i2c_send_byte_nask
    nchd12.o(.text.pcf8575_read_bit12) refers to soft_i2c.o(.text._bsp_analog_i2c_wait_ack) for _bsp_analog_i2c_wait_ack
    nchd12.o(.text.pcf8575_read_bit12) refers to soft_i2c.o(.text._bsp_analog_i2c_read_byte) for _bsp_analog_i2c_read_byte
    nchd12.o(.text.pcf8575_read_bit12) refers to soft_i2c.o(.text._bsp_analog_i2c_ack) for _bsp_analog_i2c_ack
    nchd12.o(.text.pcf8575_read_bit12) refers to soft_i2c.o(.text._bsp_analog_i2c_nack) for _bsp_analog_i2c_nack
    nchd12.o(.text.pcf8575_read_bit12) refers to soft_i2c.o(.text._bsp_analog_i2c_stop) for _bsp_analog_i2c_stop
    nchd12.o(.ARM.exidx.text.pcf8575_read_bit12) refers to nchd12.o(.text.pcf8575_read_bit12) for [Anonymous Symbol]
    nchd12.o(.text.pca9555_read_bit12) refers to soft_i2c.o(.text._bsp_analog_i2c_start) for _bsp_analog_i2c_start
    nchd12.o(.text.pca9555_read_bit12) refers to soft_i2c.o(.text._bsp_analog_i2c_send_byte_nask) for _bsp_analog_i2c_send_byte_nask
    nchd12.o(.text.pca9555_read_bit12) refers to soft_i2c.o(.text._bsp_analog_i2c_wait_ack) for _bsp_analog_i2c_wait_ack
    nchd12.o(.text.pca9555_read_bit12) refers to soft_i2c.o(.text._bsp_analog_i2c_read_byte) for _bsp_analog_i2c_read_byte
    nchd12.o(.text.pca9555_read_bit12) refers to soft_i2c.o(.text._bsp_analog_i2c_ack) for _bsp_analog_i2c_ack
    nchd12.o(.text.pca9555_read_bit12) refers to soft_i2c.o(.text._bsp_analog_i2c_nack) for _bsp_analog_i2c_nack
    nchd12.o(.text.pca9555_read_bit12) refers to soft_i2c.o(.text._bsp_analog_i2c_stop) for _bsp_analog_i2c_stop
    nchd12.o(.ARM.exidx.text.pca9555_read_bit12) refers to nchd12.o(.text.pca9555_read_bit12) for [Anonymous Symbol]
    nchd12.o(.text.pca9555_config_input) refers to soft_i2c.o(.text._bsp_analog_i2c_start) for _bsp_analog_i2c_start
    nchd12.o(.text.pca9555_config_input) refers to soft_i2c.o(.text._bsp_analog_i2c_send_byte_nask) for _bsp_analog_i2c_send_byte_nask
    nchd12.o(.text.pca9555_config_input) refers to soft_i2c.o(.text._bsp_analog_i2c_wait_ack) for _bsp_analog_i2c_wait_ack
    nchd12.o(.text.pca9555_config_input) refers to soft_i2c.o(.text._bsp_analog_i2c_stop) for _bsp_analog_i2c_stop
    nchd12.o(.ARM.exidx.text.pca9555_config_input) refers to nchd12.o(.text.pca9555_config_input) for [Anonymous Symbol]
    scheduler.o(.ARM.exidx.text.system_Task) refers to scheduler.o(.text.system_Task) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_init) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.ARM.exidx.text.scheduler_init) refers to scheduler.o(.text.scheduler_init) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.data.scheduler_task) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.bss.uwTick) for uwTick
    scheduler.o(.ARM.exidx.text.scheduler_run) refers to scheduler.o(.text.scheduler_run) for [Anonymous Symbol]
    scheduler.o(.text.SysTick_Handler) refers to scheduler.o(.bss.uwTick) for uwTick
    scheduler.o(.ARM.exidx.text.SysTick_Handler) refers to scheduler.o(.text.SysTick_Handler) for [Anonymous Symbol]
    scheduler.o(.ARM.exidx.text.DL_Delay) refers to scheduler.o(.text.DL_Delay) for [Anonymous Symbol]
    scheduler.o(.data.scheduler_task) refers to uart_driver.o(.text.uart0_task) for uart0_task
    scheduler.o(.data.scheduler_task) refers to gray.o(.text.Gray_Task) for Gray_Task
    scheduler.o(.data.scheduler_task) refers to button_driver.o(.text.key_task) for key_task
    scheduler.o(.data.scheduler_task) refers to encoder_driver.o(.text.encoder_task) for encoder_task
    scheduler.o(.data.scheduler_task) refers to pid_app.o(.text.PID_Task) for PID_Task
    scheduler.o(.data.scheduler_task) refers to scheduler.o(.text.system_Task) for system_Task
    motor.o(.text.Motor_Init) refers to motor_driver.o(.text.Motor_Stop) for Motor_Stop
    motor.o(.ARM.exidx.text.Motor_Init) refers to motor.o(.text.Motor_Init) for [Anonymous Symbol]
    pid_app.o(.text.PID_Init) refers to pid.o(.text.pid_init) for pid_init
    pid_app.o(.text.PID_Init) refers to fflti.o(.text) for __aeabi_i2f
    pid_app.o(.text.PID_Init) refers to pid.o(.text.pid_set_target) for pid_set_target
    pid_app.o(.text.PID_Init) refers to pid_app.o(.data.pid_params_left) for pid_params_left
    pid_app.o(.text.PID_Init) refers to pid_app.o(.bss.pid_speed_left) for pid_speed_left
    pid_app.o(.text.PID_Init) refers to pid_app.o(.data.pid_params_right) for pid_params_right
    pid_app.o(.text.PID_Init) refers to pid_app.o(.bss.pid_speed_right) for pid_speed_right
    pid_app.o(.text.PID_Init) refers to pid_app.o(.data.pid_params_line) for pid_params_line
    pid_app.o(.text.PID_Init) refers to pid_app.o(.bss.pid_line_gray) for pid_line_gray
    pid_app.o(.text.PID_Init) refers to pid_app.o(.data.basic_speed) for basic_speed
    pid_app.o(.ARM.exidx.text.PID_Init) refers to pid_app.o(.text.PID_Init) for [Anonymous Symbol]
    pid_app.o(.text.Line_PID_control) refers to pid.o(.text.pid_calculate_positional) for pid_calculate_positional
    pid_app.o(.text.Line_PID_control) refers to ffixi.o(.text) for __aeabi_f2iz
    pid_app.o(.text.Line_PID_control) refers to fflti.o(.text) for __aeabi_i2f
    pid_app.o(.text.Line_PID_control) refers to pid.o(.text.pid_constrain) for pid_constrain
    pid_app.o(.text.Line_PID_control) refers to pid.o(.text.pid_set_target) for pid_set_target
    pid_app.o(.text.Line_PID_control) refers to gray.o(.bss.g_line_position_error) for g_line_position_error
    pid_app.o(.text.Line_PID_control) refers to pid_app.o(.bss.pid_line_gray) for pid_line_gray
    pid_app.o(.text.Line_PID_control) refers to pid_app.o(.data.pid_params_line) for pid_params_line
    pid_app.o(.text.Line_PID_control) refers to pid_app.o(.data.basic_speed) for basic_speed
    pid_app.o(.text.Line_PID_control) refers to pid_app.o(.bss.pid_speed_left) for pid_speed_left
    pid_app.o(.text.Line_PID_control) refers to pid_app.o(.bss.pid_speed_right) for pid_speed_right
    pid_app.o(.ARM.exidx.text.Line_PID_control) refers to pid_app.o(.text.Line_PID_control) for [Anonymous Symbol]
    pid_app.o(.text.PID_Task) refers to pid.o(.text.pid_calculate_positional) for pid_calculate_positional
    pid_app.o(.text.PID_Task) refers to ffixi.o(.text) for __aeabi_f2iz
    pid_app.o(.text.PID_Task) refers to fflti.o(.text) for __aeabi_i2f
    pid_app.o(.text.PID_Task) refers to pid.o(.text.pid_constrain) for pid_constrain
    pid_app.o(.text.PID_Task) refers to pid.o(.text.pid_set_target) for pid_set_target
    pid_app.o(.text.PID_Task) refers to pid.o(.text.pid_calculate_incremental) for pid_calculate_incremental
    pid_app.o(.text.PID_Task) refers to motor_driver.o(.text.Motor_Speed_l) for Motor_Speed_l
    pid_app.o(.text.PID_Task) refers to motor_driver.o(.text.Motor_Speed_r) for Motor_Speed_r
    pid_app.o(.text.PID_Task) refers to pid_app.o(.bss.pid_running) for pid_running
    pid_app.o(.text.PID_Task) refers to pid_app.o(.data.basic_speed) for basic_speed
    pid_app.o(.text.PID_Task) refers to gray.o(.bss.g_line_position_error) for g_line_position_error
    pid_app.o(.text.PID_Task) refers to pid_app.o(.bss.pid_line_gray) for pid_line_gray
    pid_app.o(.text.PID_Task) refers to pid_app.o(.data.pid_params_line) for pid_params_line
    pid_app.o(.text.PID_Task) refers to pid_app.o(.bss.pid_speed_left) for pid_speed_left
    pid_app.o(.text.PID_Task) refers to pid_app.o(.bss.pid_speed_right) for pid_speed_right
    pid_app.o(.text.PID_Task) refers to encoder_driver.o(.bss.encoder_left) for encoder_left
    pid_app.o(.text.PID_Task) refers to encoder_driver.o(.bss.encoder_right) for encoder_right
    pid_app.o(.text.PID_Task) refers to pid_app.o(.data.pid_params_left) for pid_params_left
    pid_app.o(.text.PID_Task) refers to pid_app.o(.data.pid_params_right) for pid_params_right
    pid_app.o(.ARM.exidx.text.PID_Task) refers to pid_app.o(.text.PID_Task) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY) for [Anonymous Symbol]
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    fcmplt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmpge.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmpgt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmpeq.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    fputc_h.o(i._fputc$hlt) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc_h.o(i._fputc$hlt) refers (Special) to semi.o(.text) for __semihosting_library_function
    idiv_div0.o(.text) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    depilogue.o(i.__ARM_clz) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing startup_mspm0g350x_uvision.o(HEAP), (0 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_MOTOR_PWM_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_CLK_init), (8 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.text.user_config), (44 bytes).
    Removing main.o(.ARM.exidx.text.user_config), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing uart_driver.o(.text), (0 bytes).
    Removing uart_driver.o(.text.uart_send_char), (20 bytes).
    Removing uart_driver.o(.ARM.exidx.text.uart_send_char), (8 bytes).
    Removing uart_driver.o(.text.uart_send_string), (36 bytes).
    Removing uart_driver.o(.ARM.exidx.text.uart_send_string), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.my_printf), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.UART0_IRQHandler), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.UART1_IRQHandler), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.uart0_task), (8 bytes).
    Removing uart_driver.o(.text.uart1_task), (112 bytes).
    Removing uart_driver.o(.ARM.exidx.text.uart1_task), (8 bytes).
    Removing button_driver.o(.text), (0 bytes).
    Removing button_driver.o(.text.key_read), (20 bytes).
    Removing button_driver.o(.ARM.exidx.text.key_read), (8 bytes).
    Removing button_driver.o(.ARM.exidx.text.key_task), (8 bytes).
    Removing iic_driver.o(.text), (0 bytes).
    Removing encoder_driver.o(.text), (0 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.GROUP1_IRQHandler), (8 bytes).
    Removing encoder_driver.o(.text.encoder_init), (12 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.encoder_init), (8 bytes).
    Removing encoder_driver.o(.text.encoder_update), (80 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.encoder_update), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.encoder_task), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.encoder_config), (8 bytes).
    Removing motor_driver.o(.text), (0 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_Speed_l), (8 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_Speed_r), (8 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_Stop), (8 bytes).
    Removing pid.o(.text), (0 bytes).
    Removing pid.o(.ARM.exidx.text.pid_init), (8 bytes).
    Removing pid.o(.ARM.exidx.text.pid_set_target), (8 bytes).
    Removing pid.o(.text.pid_set_params), (4 bytes).
    Removing pid.o(.ARM.exidx.text.pid_set_params), (8 bytes).
    Removing pid.o(.text.pid_set_limit), (4 bytes).
    Removing pid.o(.ARM.exidx.text.pid_set_limit), (8 bytes).
    Removing pid.o(.ARM.exidx.text.pid_reset), (8 bytes).
    Removing pid.o(.ARM.exidx.text.pid_calculate_positional), (8 bytes).
    Removing pid.o(.ARM.exidx.text.pid_calculate_incremental), (8 bytes).
    Removing pid.o(.ARM.exidx.text.pid_constrain), (8 bytes).
    Removing pid.o(.text.pid_app_limit_integral), (44 bytes).
    Removing pid.o(.ARM.exidx.text.pid_app_limit_integral), (8 bytes).
    Removing iic.o(.text), (0 bytes).
    Removing time.o(.text), (0 bytes).
    Removing time.o(.text.delay_us), (48 bytes).
    Removing time.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing time.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing gray.o(.text), (0 bytes).
    Removing gray.o(.text.Digtal_Get_12), (80 bytes).
    Removing gray.o(.ARM.exidx.text.Digtal_Get_12), (8 bytes).
    Removing gray.o(.ARM.exidx.text.Gray_Task), (8 bytes).
    Removing soft_i2c.o(.text), (0 bytes).
    Removing soft_i2c.o(.text._i2c_sda_out), (28 bytes).
    Removing soft_i2c.o(.ARM.exidx.text._i2c_sda_out), (8 bytes).
    Removing soft_i2c.o(.text._i2c_sda_in), (28 bytes).
    Removing soft_i2c.o(.ARM.exidx.text._i2c_sda_in), (8 bytes).
    Removing soft_i2c.o(.ARM.exidx.text._bsp_analog_i2c_start), (8 bytes).
    Removing soft_i2c.o(.ARM.exidx.text._bsp_analog_i2c_stop), (8 bytes).
    Removing soft_i2c.o(.ARM.exidx.text._bsp_analog_i2c_wait_ack), (8 bytes).
    Removing soft_i2c.o(.ARM.exidx.text._bsp_analog_i2c_ack), (8 bytes).
    Removing soft_i2c.o(.ARM.exidx.text._bsp_analog_i2c_nack), (8 bytes).
    Removing soft_i2c.o(.text._bsp_analog_i2c_send_byte), (100 bytes).
    Removing soft_i2c.o(.ARM.exidx.text._bsp_analog_i2c_send_byte), (8 bytes).
    Removing soft_i2c.o(.ARM.exidx.text._bsp_analog_i2c_send_byte_nask), (8 bytes).
    Removing soft_i2c.o(.ARM.exidx.text._bsp_analog_i2c_read_byte), (8 bytes).
    Removing soft_i2c.o(.ARM.exidx.text.i2c_CheckDevice), (8 bytes).
    Removing nchd12.o(.text), (0 bytes).
    Removing nchd12.o(.text.pcf8575_read_bit12), (52 bytes).
    Removing nchd12.o(.ARM.exidx.text.pcf8575_read_bit12), (8 bytes).
    Removing nchd12.o(.ARM.exidx.text.pca9555_read_bit12), (8 bytes).
    Removing nchd12.o(.ARM.exidx.text.pca9555_config_input), (8 bytes).
    Removing scheduler.o(.text), (0 bytes).
    Removing scheduler.o(.ARM.exidx.text.system_Task), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.scheduler_init), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.scheduler_run), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing scheduler.o(.text.DL_Delay), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.DL_Delay), (8 bytes).
    Removing scheduler.o(.bss.output_ff_flag), (1 bytes).
    Removing scheduler.o(.bss.intput_timer500ms), (4 bytes).
    Removing oled.o(.text), (0 bytes).
    Removing motor.o(.text), (0 bytes).
    Removing motor.o(.text.Motor_Init), (8 bytes).
    Removing motor.o(.ARM.exidx.text.Motor_Init), (8 bytes).
    Removing pid_app.o(.text), (0 bytes).
    Removing pid_app.o(.ARM.exidx.text.PID_Init), (8 bytes).
    Removing pid_app.o(.text.Line_PID_control), (96 bytes).
    Removing pid_app.o(.ARM.exidx.text.Line_PID_control), (8 bytes).
    Removing pid_app.o(.ARM.exidx.text.PID_Task), (8 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initTimerMode), (240 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (300 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInput), (26 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (124 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (232 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (168 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (112 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (236 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (244 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (264 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (276 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode), (12 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (140 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (60 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (42 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (24 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (88 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (104 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text), (0 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT), (80 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (60 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (32 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT), (76 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC), (28 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP), (52 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY), (8 bytes).

248 unused section(s) (total 6267 bytes) removed from the image.
