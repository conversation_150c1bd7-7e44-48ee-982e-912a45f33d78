Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to scheduler.o(.text.SysTick_Handler) for SysTick_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to encoder_driver.o(.text.GROUP1_IRQHandler) for GROUP1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to uart_driver.o(.text.UART1_IRQHandler) for UART1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to uart_driver.o(.text.UART0_IRQHandler) for UART0_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) for SYSCFG_DL_MOTOR_PWM_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for SYSCFG_DL_UART_1_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init) for SYSCFG_DL_SYSCTL_CLK_init
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for DL_SYSCTL_setHFCLKSourceHFXTParams
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for DL_SYSCTL_configSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.rodata.gSYSPLLConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to ti_msp_dl_config.o(.rodata.gMOTOR_PWMClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to ti_msp_dl_config.o(.rodata.gMOTOR_PWMConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_MOTOR_PWM_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_CLK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init) for [Anonymous Symbol]
    main.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    main.o(.text.main) refers to uart_driver.o(.text.my_printf) for my_printf
    main.o(.text.main) refers to encoder_driver.o(.text.encoder_config) for encoder_config
    main.o(.text.main) refers to pid_app.o(.text.PID_Init) for PID_Init
    main.o(.text.main) refers to scheduler.o(.text.scheduler_init) for scheduler_init
    main.o(.text.main) refers to scheduler.o(.text.scheduler_run) for scheduler_run
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.user_config) refers to main.o(.text.user_config) for [Anonymous Symbol]
    uart_driver.o(.ARM.exidx.text.uart_send_char) refers to uart_driver.o(.text.uart_send_char) for [Anonymous Symbol]
    uart_driver.o(.ARM.exidx.text.uart_send_string) refers to uart_driver.o(.text.uart_send_string) for [Anonymous Symbol]
    uart_driver.o(.text.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart_driver.o(.ARM.exidx.text.my_printf) refers to uart_driver.o(.text.my_printf) for [Anonymous Symbol]
    uart_driver.o(.text.UART0_IRQHandler) refers to uart_driver.o(.bss.uart_rx_index) for uart_rx_index
    uart_driver.o(.text.UART0_IRQHandler) refers to uart_driver.o(.bss.uart_rx_buffer) for uart_rx_buffer
    uart_driver.o(.text.UART0_IRQHandler) refers to scheduler.o(.bss.uwTick) for uwTick
    uart_driver.o(.text.UART0_IRQHandler) refers to uart_driver.o(.bss.uart_tick) for uart_tick
    uart_driver.o(.ARM.exidx.text.UART0_IRQHandler) refers to uart_driver.o(.text.UART0_IRQHandler) for [Anonymous Symbol]
    uart_driver.o(.text.UART1_IRQHandler) refers to uart_driver.o(.bss.uart_rx_index_1) for uart_rx_index_1
    uart_driver.o(.text.UART1_IRQHandler) refers to uart_driver.o(.bss.uart_rx_buffer_1) for uart_rx_buffer_1
    uart_driver.o(.text.UART1_IRQHandler) refers to scheduler.o(.bss.uwTick) for uwTick
    uart_driver.o(.text.UART1_IRQHandler) refers to uart_driver.o(.bss.uart_tick_1) for uart_tick_1
    uart_driver.o(.ARM.exidx.text.UART1_IRQHandler) refers to uart_driver.o(.text.UART1_IRQHandler) for [Anonymous Symbol]
    uart_driver.o(.text.uart0_task) refers to uart_driver.o(.text.my_printf) for my_printf
    uart_driver.o(.text.uart0_task) refers to memseta.o(.text) for __aeabi_memclr4
    uart_driver.o(.text.uart0_task) refers to uart_driver.o(.bss.uart_rx_index) for uart_rx_index
    uart_driver.o(.text.uart0_task) refers to uart_driver.o(.bss.uart_tick) for uart_tick
    uart_driver.o(.text.uart0_task) refers to scheduler.o(.bss.uwTick) for uwTick
    uart_driver.o(.text.uart0_task) refers to uart_driver.o(.rodata.str1.1) for [Anonymous Symbol]
    uart_driver.o(.text.uart0_task) refers to uart_driver.o(.bss.uart_rx_buffer) for uart_rx_buffer
    uart_driver.o(.ARM.exidx.text.uart0_task) refers to uart_driver.o(.text.uart0_task) for [Anonymous Symbol]
    uart_driver.o(.text.uart1_task) refers to uart_driver.o(.text.my_printf) for my_printf
    uart_driver.o(.text.uart1_task) refers to memseta.o(.text) for __aeabi_memclr4
    uart_driver.o(.text.uart1_task) refers to uart_driver.o(.bss.uart_rx_index_1) for uart_rx_index_1
    uart_driver.o(.text.uart1_task) refers to uart_driver.o(.bss.uart_tick_1) for uart_tick_1
    uart_driver.o(.text.uart1_task) refers to scheduler.o(.bss.uwTick) for uwTick
    uart_driver.o(.text.uart1_task) refers to uart_driver.o(.rodata.str1.1) for [Anonymous Symbol]
    uart_driver.o(.text.uart1_task) refers to uart_driver.o(.bss.uart_rx_buffer_1) for uart_rx_buffer_1
    uart_driver.o(.ARM.exidx.text.uart1_task) refers to uart_driver.o(.text.uart1_task) for [Anonymous Symbol]
    button_driver.o(.ARM.exidx.text.key_read) refers to button_driver.o(.text.key_read) for [Anonymous Symbol]
    button_driver.o(.text.key_task) refers to motor_driver.o(.text.Motor_Stop) for Motor_Stop
    button_driver.o(.text.key_task) refers to uart_driver.o(.text.my_printf) for my_printf
    button_driver.o(.text.key_task) refers to button_driver.o(.bss.key_val) for key_val
    button_driver.o(.text.key_task) refers to button_driver.o(.bss.key_old) for key_old
    button_driver.o(.text.key_task) refers to button_driver.o(.bss.key_down) for key_down
    button_driver.o(.text.key_task) refers to button_driver.o(.bss.key_up) for key_up
    button_driver.o(.text.key_task) refers to button_driver.o(.bss.led_state) for led_state
    button_driver.o(.text.key_task) refers to pid_app.o(.bss.pid_running) for pid_running
    button_driver.o(.ARM.exidx.text.key_task) refers to button_driver.o(.text.key_task) for [Anonymous Symbol]
    encoder_driver.o(.text.GROUP1_IRQHandler) refers to encoder_driver.o(.bss.encoder_get_count) for encoder_get_count
    encoder_driver.o(.ARM.exidx.text.GROUP1_IRQHandler) refers to encoder_driver.o(.text.GROUP1_IRQHandler) for [Anonymous Symbol]
    encoder_driver.o(.ARM.exidx.text.encoder_init) refers to encoder_driver.o(.text.encoder_init) for [Anonymous Symbol]
    encoder_driver.o(.text.encoder_update) refers to fflti.o(.text) for __aeabi_i2f
    encoder_driver.o(.text.encoder_update) refers to fdiv.o(.text) for __aeabi_fdiv
    encoder_driver.o(.text.encoder_update) refers to fmul.o(.text) for __aeabi_fmul
    encoder_driver.o(.text.encoder_update) refers to encoder_driver.o(.bss.encoder_get_count) for encoder_get_count
    encoder_driver.o(.ARM.exidx.text.encoder_update) refers to encoder_driver.o(.text.encoder_update) for [Anonymous Symbol]
    encoder_driver.o(.text.encoder_task) refers to fflti.o(.text) for __aeabi_i2f
    encoder_driver.o(.text.encoder_task) refers to fdiv.o(.text) for __aeabi_fdiv
    encoder_driver.o(.text.encoder_task) refers to fmul.o(.text) for __aeabi_fmul
    encoder_driver.o(.text.encoder_task) refers to encoder_driver.o(.bss.encoder_left) for encoder_left
    encoder_driver.o(.text.encoder_task) refers to encoder_driver.o(.bss.encoder_get_count) for encoder_get_count
    encoder_driver.o(.text.encoder_task) refers to encoder_driver.o(.bss.encoder_right) for encoder_right
    encoder_driver.o(.ARM.exidx.text.encoder_task) refers to encoder_driver.o(.text.encoder_task) for [Anonymous Symbol]
    encoder_driver.o(.text.encoder_config) refers to encoder_driver.o(.bss.encoder_left) for encoder_left
    encoder_driver.o(.text.encoder_config) refers to encoder_driver.o(.bss.encoder_right) for encoder_right
    encoder_driver.o(.ARM.exidx.text.encoder_config) refers to encoder_driver.o(.text.encoder_config) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_Speed_l) refers to fcmpgt.o(.text) for __aeabi_fcmpgt
    motor_driver.o(.text.Motor_Speed_l) refers to fcmpeq.o(.text) for __aeabi_fcmpeq
    motor_driver.o(.text.Motor_Speed_l) refers to fcmpge.o(.text) for __aeabi_fcmpge
    motor_driver.o(.text.Motor_Speed_l) refers to fmul.o(.text) for __aeabi_fmul
    motor_driver.o(.text.Motor_Speed_l) refers to ffixui.o(.text) for __aeabi_f2uiz
    motor_driver.o(.text.Motor_Speed_l) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_driver.o(.ARM.exidx.text.Motor_Speed_l) refers to motor_driver.o(.text.Motor_Speed_l) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_Speed_r) refers to fcmpgt.o(.text) for __aeabi_fcmpgt
    motor_driver.o(.text.Motor_Speed_r) refers to fcmpeq.o(.text) for __aeabi_fcmpeq
    motor_driver.o(.text.Motor_Speed_r) refers to fcmpge.o(.text) for __aeabi_fcmpge
    motor_driver.o(.text.Motor_Speed_r) refers to fmul.o(.text) for __aeabi_fmul
    motor_driver.o(.text.Motor_Speed_r) refers to ffixui.o(.text) for __aeabi_f2uiz
    motor_driver.o(.text.Motor_Speed_r) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_driver.o(.ARM.exidx.text.Motor_Speed_r) refers to motor_driver.o(.text.Motor_Speed_r) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_Stop) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_driver.o(.ARM.exidx.text.Motor_Stop) refers to motor_driver.o(.text.Motor_Stop) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_init) refers to pid.o(.text.pid_init) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_set_target) refers to pid.o(.text.pid_set_target) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_set_params) refers to pid.o(.text.pid_set_params) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_set_limit) refers to pid.o(.text.pid_set_limit) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_reset) refers to pid.o(.text.pid_reset) for [Anonymous Symbol]
    pid.o(.text.pid_calculate_positional) refers to fadd.o(.text) for __aeabi_fsub
    pid.o(.text.pid_calculate_positional) refers to fmul.o(.text) for __aeabi_fmul
    pid.o(.text.pid_calculate_positional) refers to fcmpgt.o(.text) for __aeabi_fcmpgt
    pid.o(.text.pid_calculate_positional) refers to fcmpge.o(.text) for __aeabi_fcmpge
    pid.o(.ARM.exidx.text.pid_calculate_positional) refers to pid.o(.text.pid_calculate_positional) for [Anonymous Symbol]
    pid.o(.text.pid_calculate_incremental) refers to fadd.o(.text) for __aeabi_fsub
    pid.o(.text.pid_calculate_incremental) refers to fmul.o(.text) for __aeabi_fmul
    pid.o(.text.pid_calculate_incremental) refers to fcmpgt.o(.text) for __aeabi_fcmpgt
    pid.o(.text.pid_calculate_incremental) refers to fcmpge.o(.text) for __aeabi_fcmpge
    pid.o(.ARM.exidx.text.pid_calculate_incremental) refers to pid.o(.text.pid_calculate_incremental) for [Anonymous Symbol]
    pid.o(.text.pid_constrain) refers to fcmplt.o(.text) for __aeabi_fcmplt
    pid.o(.text.pid_constrain) refers to fcmpgt.o(.text) for __aeabi_fcmpgt
    pid.o(.ARM.exidx.text.pid_constrain) refers to pid.o(.text.pid_constrain) for [Anonymous Symbol]
    pid.o(.text.pid_app_limit_integral) refers to fcmpgt.o(.text) for __aeabi_fcmpgt
    pid.o(.text.pid_app_limit_integral) refers to fcmpge.o(.text) for __aeabi_fcmpge
    pid.o(.ARM.exidx.text.pid_app_limit_integral) refers to pid.o(.text.pid_app_limit_integral) for [Anonymous Symbol]
    time.o(.ARM.exidx.text.delay_us) refers to time.o(.text.delay_us) for [Anonymous Symbol]
    time.o(.ARM.exidx.text.delay_ms) refers to time.o(.text.delay_ms) for [Anonymous Symbol]
    gray.o(.ARM.exidx.text.Digtal_Get) refers to gray.o(.text.Digtal_Get) for [Anonymous Symbol]
    gray.o(.text.Gray_Task) refers to fadd.o(.text) for __aeabi_fadd
    gray.o(.text.Gray_Task) refers to ffltui.o(.text) for __aeabi_ui2f
    gray.o(.text.Gray_Task) refers to fdiv.o(.text) for __aeabi_fdiv
    gray.o(.text.Gray_Task) refers to motor_driver.o(.text.Motor_Stop) for Motor_Stop
    gray.o(.text.Gray_Task) refers to motor_driver.o(.text.Motor_Speed_l) for Motor_Speed_l
    gray.o(.text.Gray_Task) refers to motor_driver.o(.text.Motor_Speed_r) for Motor_Speed_r
    gray.o(.text.Gray_Task) refers to time.o(.text.delay_ms) for delay_ms
    gray.o(.text.Gray_Task) refers to pid.o(.text.pid_reset) for pid_reset
    gray.o(.text.Gray_Task) refers to fflti.o(.text) for __aeabi_i2f
    gray.o(.text.Gray_Task) refers to pid.o(.text.pid_set_target) for pid_set_target
    gray.o(.text.Gray_Task) refers to gray.o(.bss.Digtal) for Digtal
    gray.o(.text.Gray_Task) refers to gray.o(.data.gray_weights) for gray_weights
    gray.o(.text.Gray_Task) refers to gray.o(.bss.g_line_position_error) for g_line_position_error
    gray.o(.text.Gray_Task) refers to pid_app.o(.bss.pid_running) for pid_running
    gray.o(.text.Gray_Task) refers to gray.o(.bss.baochi_flag) for baochi_flag
    gray.o(.text.Gray_Task) refers to scheduler.o(.bss.L_count) for L_count
    gray.o(.text.Gray_Task) refers to pid_app.o(.data.basic_speed) for basic_speed
    gray.o(.text.Gray_Task) refers to pid_app.o(.bss.pid_speed_left) for pid_speed_left
    gray.o(.text.Gray_Task) refers to pid_app.o(.bss.pid_speed_right) for pid_speed_right
    gray.o(.text.Gray_Task) refers to pid_app.o(.bss.pid_line_gray) for pid_line_gray
    gray.o(.ARM.exidx.text.Gray_Task) refers to gray.o(.text.Gray_Task) for [Anonymous Symbol]
    scheduler.o(.ARM.exidx.text.system_Task) refers to scheduler.o(.text.system_Task) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_init) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.ARM.exidx.text.scheduler_init) refers to scheduler.o(.text.scheduler_init) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.data.scheduler_task) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.bss.uwTick) for uwTick
    scheduler.o(.ARM.exidx.text.scheduler_run) refers to scheduler.o(.text.scheduler_run) for [Anonymous Symbol]
    scheduler.o(.text.SysTick_Handler) refers to scheduler.o(.bss.uwTick) for uwTick
    scheduler.o(.ARM.exidx.text.SysTick_Handler) refers to scheduler.o(.text.SysTick_Handler) for [Anonymous Symbol]
    scheduler.o(.ARM.exidx.text.DL_Delay) refers to scheduler.o(.text.DL_Delay) for [Anonymous Symbol]
    scheduler.o(.data.scheduler_task) refers to uart_driver.o(.text.uart0_task) for uart0_task
    scheduler.o(.data.scheduler_task) refers to gray.o(.text.Gray_Task) for Gray_Task
    scheduler.o(.data.scheduler_task) refers to button_driver.o(.text.key_task) for key_task
    scheduler.o(.data.scheduler_task) refers to encoder_driver.o(.text.encoder_task) for encoder_task
    scheduler.o(.data.scheduler_task) refers to pid_app.o(.text.PID_Task) for PID_Task
    scheduler.o(.data.scheduler_task) refers to scheduler.o(.text.system_Task) for system_Task
    motor.o(.text.Motor_Init) refers to motor_driver.o(.text.Motor_Stop) for Motor_Stop
    motor.o(.ARM.exidx.text.Motor_Init) refers to motor.o(.text.Motor_Init) for [Anonymous Symbol]
    pid_app.o(.text.PID_Init) refers to pid.o(.text.pid_init) for pid_init
    pid_app.o(.text.PID_Init) refers to fflti.o(.text) for __aeabi_i2f
    pid_app.o(.text.PID_Init) refers to pid.o(.text.pid_set_target) for pid_set_target
    pid_app.o(.text.PID_Init) refers to pid_app.o(.data.pid_params_left) for pid_params_left
    pid_app.o(.text.PID_Init) refers to pid_app.o(.bss.pid_speed_left) for pid_speed_left
    pid_app.o(.text.PID_Init) refers to pid_app.o(.data.pid_params_right) for pid_params_right
    pid_app.o(.text.PID_Init) refers to pid_app.o(.bss.pid_speed_right) for pid_speed_right
    pid_app.o(.text.PID_Init) refers to pid_app.o(.data.pid_params_line) for pid_params_line
    pid_app.o(.text.PID_Init) refers to pid_app.o(.bss.pid_line_gray) for pid_line_gray
    pid_app.o(.text.PID_Init) refers to pid_app.o(.data.basic_speed) for basic_speed
    pid_app.o(.ARM.exidx.text.PID_Init) refers to pid_app.o(.text.PID_Init) for [Anonymous Symbol]
    pid_app.o(.text.Line_PID_control) refers to pid.o(.text.pid_calculate_positional) for pid_calculate_positional
    pid_app.o(.text.Line_PID_control) refers to ffixi.o(.text) for __aeabi_f2iz
    pid_app.o(.text.Line_PID_control) refers to fflti.o(.text) for __aeabi_i2f
    pid_app.o(.text.Line_PID_control) refers to pid.o(.text.pid_constrain) for pid_constrain
    pid_app.o(.text.Line_PID_control) refers to pid.o(.text.pid_set_target) for pid_set_target
    pid_app.o(.text.Line_PID_control) refers to gray.o(.bss.g_line_position_error) for g_line_position_error
    pid_app.o(.text.Line_PID_control) refers to pid_app.o(.bss.pid_line_gray) for pid_line_gray
    pid_app.o(.text.Line_PID_control) refers to pid_app.o(.data.pid_params_line) for pid_params_line
    pid_app.o(.text.Line_PID_control) refers to pid_app.o(.data.basic_speed) for basic_speed
    pid_app.o(.text.Line_PID_control) refers to pid_app.o(.bss.pid_speed_left) for pid_speed_left
    pid_app.o(.text.Line_PID_control) refers to pid_app.o(.bss.pid_speed_right) for pid_speed_right
    pid_app.o(.ARM.exidx.text.Line_PID_control) refers to pid_app.o(.text.Line_PID_control) for [Anonymous Symbol]
    pid_app.o(.text.PID_Task) refers to pid.o(.text.pid_calculate_positional) for pid_calculate_positional
    pid_app.o(.text.PID_Task) refers to ffixi.o(.text) for __aeabi_f2iz
    pid_app.o(.text.PID_Task) refers to fflti.o(.text) for __aeabi_i2f
    pid_app.o(.text.PID_Task) refers to pid.o(.text.pid_constrain) for pid_constrain
    pid_app.o(.text.PID_Task) refers to pid.o(.text.pid_set_target) for pid_set_target
    pid_app.o(.text.PID_Task) refers to pid.o(.text.pid_calculate_incremental) for pid_calculate_incremental
    pid_app.o(.text.PID_Task) refers to motor_driver.o(.text.Motor_Speed_l) for Motor_Speed_l
    pid_app.o(.text.PID_Task) refers to motor_driver.o(.text.Motor_Speed_r) for Motor_Speed_r
    pid_app.o(.text.PID_Task) refers to pid_app.o(.bss.pid_running) for pid_running
    pid_app.o(.text.PID_Task) refers to pid_app.o(.data.basic_speed) for basic_speed
    pid_app.o(.text.PID_Task) refers to gray.o(.bss.g_line_position_error) for g_line_position_error
    pid_app.o(.text.PID_Task) refers to pid_app.o(.bss.pid_line_gray) for pid_line_gray
    pid_app.o(.text.PID_Task) refers to pid_app.o(.data.pid_params_line) for pid_params_line
    pid_app.o(.text.PID_Task) refers to pid_app.o(.bss.pid_speed_left) for pid_speed_left
    pid_app.o(.text.PID_Task) refers to pid_app.o(.bss.pid_speed_right) for pid_speed_right
    pid_app.o(.text.PID_Task) refers to encoder_driver.o(.bss.encoder_left) for encoder_left
    pid_app.o(.text.PID_Task) refers to encoder_driver.o(.bss.encoder_right) for encoder_right
    pid_app.o(.text.PID_Task) refers to pid_app.o(.data.pid_params_left) for pid_params_left
    pid_app.o(.text.PID_Task) refers to pid_app.o(.data.pid_params_right) for pid_params_right
    pid_app.o(.ARM.exidx.text.PID_Task) refers to pid_app.o(.text.PID_Task) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY) for [Anonymous Symbol]
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    fcmplt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmpge.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmpgt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmpeq.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    fputc_h.o(i._fputc$hlt) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc_h.o(i._fputc$hlt) refers (Special) to semi.o(.text) for __semihosting_library_function
    idiv_div0.o(.text) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    depilogue.o(i.__ARM_clz) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing startup_mspm0g350x_uvision.o(HEAP), (0 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_MOTOR_PWM_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_CLK_init), (8 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.text.user_config), (32 bytes).
    Removing main.o(.ARM.exidx.text.user_config), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing uart_driver.o(.text), (0 bytes).
    Removing uart_driver.o(.text.uart_send_char), (20 bytes).
    Removing uart_driver.o(.ARM.exidx.text.uart_send_char), (8 bytes).
    Removing uart_driver.o(.text.uart_send_string), (36 bytes).
    Removing uart_driver.o(.ARM.exidx.text.uart_send_string), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.my_printf), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.UART0_IRQHandler), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.UART1_IRQHandler), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.uart0_task), (8 bytes).
    Removing uart_driver.o(.text.uart1_task), (112 bytes).
    Removing uart_driver.o(.ARM.exidx.text.uart1_task), (8 bytes).
    Removing button_driver.o(.text), (0 bytes).
    Removing button_driver.o(.text.key_read), (20 bytes).
    Removing button_driver.o(.ARM.exidx.text.key_read), (8 bytes).
    Removing button_driver.o(.ARM.exidx.text.key_task), (8 bytes).
    Removing iic_driver.o(.text), (0 bytes).
    Removing encoder_driver.o(.text), (0 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.GROUP1_IRQHandler), (8 bytes).
    Removing encoder_driver.o(.text.encoder_init), (12 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.encoder_init), (8 bytes).
    Removing encoder_driver.o(.text.encoder_update), (80 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.encoder_update), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.encoder_task), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.encoder_config), (8 bytes).
    Removing motor_driver.o(.text), (0 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_Speed_l), (8 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_Speed_r), (8 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_Stop), (8 bytes).
    Removing pid.o(.text), (0 bytes).
    Removing pid.o(.ARM.exidx.text.pid_init), (8 bytes).
    Removing pid.o(.ARM.exidx.text.pid_set_target), (8 bytes).
    Removing pid.o(.text.pid_set_params), (4 bytes).
    Removing pid.o(.ARM.exidx.text.pid_set_params), (8 bytes).
    Removing pid.o(.text.pid_set_limit), (4 bytes).
    Removing pid.o(.ARM.exidx.text.pid_set_limit), (8 bytes).
    Removing pid.o(.ARM.exidx.text.pid_reset), (8 bytes).
    Removing pid.o(.ARM.exidx.text.pid_calculate_positional), (8 bytes).
    Removing pid.o(.ARM.exidx.text.pid_calculate_incremental), (8 bytes).
    Removing pid.o(.ARM.exidx.text.pid_constrain), (8 bytes).
    Removing pid.o(.text.pid_app_limit_integral), (44 bytes).
    Removing pid.o(.ARM.exidx.text.pid_app_limit_integral), (8 bytes).
    Removing iic.o(.text), (0 bytes).
    Removing time.o(.text), (0 bytes).
    Removing time.o(.text.delay_us), (48 bytes).
    Removing time.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing time.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing gray.o(.text), (0 bytes).
    Removing gray.o(.text.Digtal_Get), (76 bytes).
    Removing gray.o(.ARM.exidx.text.Digtal_Get), (8 bytes).
    Removing gray.o(.ARM.exidx.text.Gray_Task), (8 bytes).
    Removing scheduler.o(.text), (0 bytes).
    Removing scheduler.o(.ARM.exidx.text.system_Task), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.scheduler_init), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.scheduler_run), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing scheduler.o(.text.DL_Delay), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.DL_Delay), (8 bytes).
    Removing scheduler.o(.bss.output_ff_flag), (1 bytes).
    Removing scheduler.o(.bss.intput_timer500ms), (4 bytes).
    Removing oled.o(.text), (0 bytes).
    Removing motor.o(.text), (0 bytes).
    Removing motor.o(.text.Motor_Init), (8 bytes).
    Removing motor.o(.ARM.exidx.text.Motor_Init), (8 bytes).
    Removing pid_app.o(.text), (0 bytes).
    Removing pid_app.o(.ARM.exidx.text.PID_Init), (8 bytes).
    Removing pid_app.o(.text.Line_PID_control), (96 bytes).
    Removing pid_app.o(.ARM.exidx.text.Line_PID_control), (8 bytes).
    Removing pid_app.o(.ARM.exidx.text.PID_Task), (8 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initTimerMode), (240 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (300 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInput), (26 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (124 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (232 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (168 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (112 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (236 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (244 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (264 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (276 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode), (12 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (140 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (60 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (42 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (24 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (88 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (104 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text), (0 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT), (80 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (60 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (32 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT), (76 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC), (28 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP), (52 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY), (8 bytes).

228 unused section(s) (total 5931 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc_h.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmplt.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmpge.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmpgt.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmpeq.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    IIC.c                                    0x00000000   Number         0  iic.o ABSOLUTE
    OLED.c                                   0x00000000   Number         0  oled.o ABSOLUTE
    Time.c                                   0x00000000   Number         0  time.o ABSOLUTE
    button_driver.c                          0x00000000   Number         0  button_driver.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_sysctl_mspm0g1x0x_g3x0x.c             0x00000000   Number         0  dl_sysctl_mspm0g1x0x_g3x0x.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    encoder_driver.c                         0x00000000   Number         0  encoder_driver.o ABSOLUTE
    gray.c                                   0x00000000   Number         0  gray.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    iic_driver.c                             0x00000000   Number         0  iic_driver.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    motor.c                                  0x00000000   Number         0  motor.o ABSOLUTE
    motor_driver.c                           0x00000000   Number         0  motor_driver.o ABSOLUTE
    pid.c                                    0x00000000   Number         0  pid.o ABSOLUTE
    pid_app.c                                0x00000000   Number         0  pid_app.o ABSOLUTE
    scheduler.c                              0x00000000   Number         0  scheduler.o ABSOLUTE
    startup_mspm0g350x_uvision.s             0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    uart_driver.c                            0x00000000   Number         0  uart_driver.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    .ARM.Collect$$$$00000000                 0x000000c0   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x000000c0   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x000000c4   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x000000c8   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x000000c8   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x000000c8   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x000000d0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x000000d0   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x000000d0   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x000000d0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x000000d4   Section       20  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000000e8   Section        0  memseta.o(.text)
    .text                                    0x0000010c   Section        0  fadd.o(.text)
    .text                                    0x000001be   Section        0  fmul.o(.text)
    .text                                    0x00000238   Section        0  fdiv.o(.text)
    .text                                    0x000002b4   Section        0  fcmplt.o(.text)
    .text                                    0x000002d0   Section        0  fcmpge.o(.text)
    .text                                    0x000002ec   Section        0  fcmpgt.o(.text)
    .text                                    0x00000308   Section        0  fcmpeq.o(.text)
    .text                                    0x00000324   Section        0  fflti.o(.text)
    .text                                    0x0000033a   Section        0  ffltui.o(.text)
    .text                                    0x00000348   Section        0  ffixi.o(.text)
    .text                                    0x0000037a   Section        0  ffixui.o(.text)
    .text                                    0x000003a2   Section        0  uidiv_div0.o(.text)
    .text                                    0x000003e0   Section        0  uldiv.o(.text)
    .text                                    0x00000440   Section        0  iusefp.o(.text)
    .text                                    0x00000440   Section        0  fepilogue.o(.text)
    .text                                    0x000004c4   Section        0  dadd.o(.text)
    .text                                    0x00000628   Section        0  dmul.o(.text)
    .text                                    0x000006f8   Section        0  ddiv.o(.text)
    .text                                    0x000007e8   Section        0  dfixul.o(.text)
    .text                                    0x00000828   Section       40  cdrcmple.o(.text)
    .text                                    0x00000850   Section       48  init.o(.text)
    .text                                    0x00000880   Section        0  llshl.o(.text)
    .text                                    0x000008a0   Section        0  llushr.o(.text)
    .text                                    0x000008c2   Section        0  llsshr.o(.text)
    .text                                    0x000008e8   Section        0  depilogue.o(.text)
    [Anonymous Symbol]                       0x000009a6   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    [Anonymous Symbol]                       0x000009b0   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_0                             0x00000a64   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_2                             0x00000a68   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_3                             0x00000a6c   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_4                             0x00000a70   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    [Anonymous Symbol]                       0x00000a74   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    __arm_cp.7_0                             0x00000ac0   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    [Anonymous Symbol]                       0x00000ac4   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_0                             0x00000ae4   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_1                             0x00000ae8   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    [Anonymous Symbol]                       0x00000aec   Section        0  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_0                            0x00000be0   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_1                            0x00000be4   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_2                            0x00000be8   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_3                            0x00000bec   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_4                            0x00000bf0   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_5                            0x00000bf4   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_6                            0x00000bf8   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    [Anonymous Symbol]                       0x00000bfc   Section        0  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    __arm_cp.23_0                            0x00000c14   Number         4  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    [Anonymous Symbol]                       0x00000c18   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    __arm_cp.27_0                            0x00000c2c   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    [Anonymous Symbol]                       0x00000c30   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    __arm_cp.3_0                             0x00000c3c   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    [Anonymous Symbol]                       0x00000c40   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    __arm_cp.0_0                             0x00000c58   Number         4  dl_timer.o(.text.DL_Timer_setClockConfig)
    [Anonymous Symbol]                       0x00000c5c   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x00000c9c   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x00000ca0   Number         4  dl_uart.o(.text.DL_UART_init)
    [Anonymous Symbol]                       0x00000ca4   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    [Anonymous Symbol]                       0x00000cb8   Section        0  encoder_driver.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_0                             0x00000d54   Number         4  encoder_driver.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_1                             0x00000d58   Number         4  encoder_driver.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_2                             0x00000d5c   Number         4  encoder_driver.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_4                             0x00000d60   Number         4  encoder_driver.o(.text.GROUP1_IRQHandler)
    [Anonymous Symbol]                       0x00000d64   Section        0  gray.o(.text.Gray_Task)
    __arm_cp.1_0                             0x00000e80   Number         4  gray.o(.text.Gray_Task)
    __arm_cp.1_1                             0x00000e84   Number         4  gray.o(.text.Gray_Task)
    __arm_cp.1_2                             0x00000e88   Number         4  gray.o(.text.Gray_Task)
    __arm_cp.1_5                             0x00000e8c   Number         4  gray.o(.text.Gray_Task)
    __arm_cp.1_6                             0x00000e90   Number         4  gray.o(.text.Gray_Task)
    __arm_cp.1_7                             0x00000e94   Number         4  gray.o(.text.Gray_Task)
    __arm_cp.1_8                             0x00000e98   Number         4  gray.o(.text.Gray_Task)
    [Anonymous Symbol]                       0x00000e9c   Section        0  motor_driver.o(.text.Motor_Speed_l)
    [Anonymous Symbol]                       0x00000f10   Section        0  motor_driver.o(.text.Motor_Speed_r)
    __arm_cp.1_0                             0x00000f8c   Number         4  motor_driver.o(.text.Motor_Speed_r)
    __arm_cp.1_2                             0x00000f90   Number         4  motor_driver.o(.text.Motor_Speed_r)
    __arm_cp.1_3                             0x00000f94   Number         4  motor_driver.o(.text.Motor_Speed_r)
    [Anonymous Symbol]                       0x00000f98   Section        0  motor_driver.o(.text.Motor_Stop)
    __arm_cp.2_0                             0x00000fcc   Number         4  motor_driver.o(.text.Motor_Stop)
    [Anonymous Symbol]                       0x00000fd0   Section        0  pid_app.o(.text.PID_Init)
    [Anonymous Symbol]                       0x00001034   Section        0  pid_app.o(.text.PID_Task)
    __arm_cp.2_0                             0x000010cc   Number         4  pid_app.o(.text.PID_Task)
    __arm_cp.2_1                             0x000010d0   Number         4  pid_app.o(.text.PID_Task)
    __arm_cp.2_2                             0x000010d4   Number         4  pid_app.o(.text.PID_Task)
    __arm_cp.2_3                             0x000010d8   Number         4  pid_app.o(.text.PID_Task)
    __arm_cp.2_4                             0x000010dc   Number         4  pid_app.o(.text.PID_Task)
    __arm_cp.2_5                             0x000010e0   Number         4  pid_app.o(.text.PID_Task)
    __arm_cp.2_6                             0x000010e4   Number         4  pid_app.o(.text.PID_Task)
    __arm_cp.2_7                             0x000010e8   Number         4  pid_app.o(.text.PID_Task)
    __arm_cp.2_8                             0x000010ec   Number         4  pid_app.o(.text.PID_Task)
    __arm_cp.2_9                             0x000010f0   Number         4  pid_app.o(.text.PID_Task)
    __arm_cp.2_10                            0x000010f4   Number         4  pid_app.o(.text.PID_Task)
    [Anonymous Symbol]                       0x000010f8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_0                             0x00001188   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_1                             0x0000118c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x00001190   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_3                             0x00001194   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_4                             0x00001198   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_5                             0x0000119c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_6                             0x000011a0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_7                             0x000011a4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_8                             0x000011a8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_9                             0x000011ac   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_10                            0x000011b0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_11                            0x000011b4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_12                            0x000011b8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x000011bc   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_0                             0x00001230   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_1                             0x00001234   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_2                             0x00001238   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_3                             0x0000123c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_4                             0x00001240   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_5                             0x00001244   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    [Anonymous Symbol]                       0x00001248   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init)
    __arm_cp.8_0                             0x00001258   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init)
    __arm_cp.8_1                             0x0000125c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init)
    [Anonymous Symbol]                       0x00001260   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x000012c0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_1                             0x000012c4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_2                             0x000012c8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x000012cc   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    __arm_cp.7_0                             0x000012f0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    __arm_cp.7_1                             0x000012f4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    __arm_cp.7_2                             0x000012f8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x000012fc   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.5_1                             0x00001354   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.5_2                             0x00001358   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.5_3                             0x0000135c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.5_5                             0x00001360   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x00001364   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.6_0                             0x000013bc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.6_1                             0x000013c0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.6_2                             0x000013c4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.6_3                             0x000013c8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.6_4                             0x000013cc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.6_5                             0x000013d0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    [Anonymous Symbol]                       0x000013d4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x000013f8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x00001424   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x00001428   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x0000142c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x00001430   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_4                             0x00001434   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_5                             0x00001438   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_6                             0x0000143c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x00001440   Section        0  scheduler.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x0000144c   Section        0  uart_driver.o(.text.UART0_IRQHandler)
    __arm_cp.3_0                             0x0000147c   Number         4  uart_driver.o(.text.UART0_IRQHandler)
    __arm_cp.3_1                             0x00001480   Number         4  uart_driver.o(.text.UART0_IRQHandler)
    __arm_cp.3_2                             0x00001484   Number         4  uart_driver.o(.text.UART0_IRQHandler)
    __arm_cp.3_3                             0x00001488   Number         4  uart_driver.o(.text.UART0_IRQHandler)
    __arm_cp.3_5                             0x0000148c   Number         4  uart_driver.o(.text.UART0_IRQHandler)
    [Anonymous Symbol]                       0x00001490   Section        0  uart_driver.o(.text.UART1_IRQHandler)
    __arm_cp.4_0                             0x000014c0   Number         4  uart_driver.o(.text.UART1_IRQHandler)
    __arm_cp.4_1                             0x000014c4   Number         4  uart_driver.o(.text.UART1_IRQHandler)
    __arm_cp.4_2                             0x000014c8   Number         4  uart_driver.o(.text.UART1_IRQHandler)
    __arm_cp.4_3                             0x000014cc   Number         4  uart_driver.o(.text.UART1_IRQHandler)
    __arm_cp.4_4                             0x000014d0   Number         4  uart_driver.o(.text.UART1_IRQHandler)
    __arm_cp.4_5                             0x000014d4   Number         4  uart_driver.o(.text.UART1_IRQHandler)
    [Anonymous Symbol]                       0x000014d8   Section        0  time.o(.text.delay_ms)
    __arm_cp.1_0                             0x00001504   Number         4  time.o(.text.delay_ms)
    __arm_cp.1_1                             0x00001508   Number         4  time.o(.text.delay_ms)
    [Anonymous Symbol]                       0x0000150c   Section        0  encoder_driver.o(.text.encoder_config)
    [Anonymous Symbol]                       0x00001520   Section        0  encoder_driver.o(.text.encoder_task)
    __arm_cp.3_0                             0x00001598   Number         4  encoder_driver.o(.text.encoder_task)
    __arm_cp.3_1                             0x0000159c   Number         4  encoder_driver.o(.text.encoder_task)
    __arm_cp.3_2                             0x000015a0   Number         4  encoder_driver.o(.text.encoder_task)
    __arm_cp.3_3                             0x000015a4   Number         4  encoder_driver.o(.text.encoder_task)
    __arm_cp.3_4                             0x000015a8   Number         4  encoder_driver.o(.text.encoder_task)
    __arm_cp.3_5                             0x000015ac   Number         4  encoder_driver.o(.text.encoder_task)
    [Anonymous Symbol]                       0x000015b0   Section        0  button_driver.o(.text.key_task)
    __arm_cp.1_0                             0x00001614   Number         4  button_driver.o(.text.key_task)
    __arm_cp.1_1                             0x00001618   Number         4  button_driver.o(.text.key_task)
    __arm_cp.1_2                             0x0000161c   Number         4  button_driver.o(.text.key_task)
    __arm_cp.1_3                             0x00001620   Number         4  button_driver.o(.text.key_task)
    __arm_cp.1_4                             0x00001624   Number         4  button_driver.o(.text.key_task)
    __arm_cp.1_5                             0x00001628   Number         4  button_driver.o(.text.key_task)
    __arm_cp.1_6                             0x0000162c   Number         4  button_driver.o(.text.key_task)
    __arm_cp.1_7                             0x00001630   Number         4  button_driver.o(.text.key_task)
    __arm_cp.1_8                             0x00001634   Number         4  button_driver.o(.text.key_task)
    [Anonymous Symbol]                       0x00001648   Section        0  main.o(.text.main)
    __arm_cp.0_0                             0x0000167c   Number         4  main.o(.text.main)
    __arm_cp.0_2                             0x00001688   Number         4  main.o(.text.main)
    __arm_cp.0_3                             0x0000168c   Number         4  main.o(.text.main)
    [Anonymous Symbol]                       0x00001690   Section        0  uart_driver.o(.text.my_printf)
    __arm_cp.2_0                             0x000016d4   Number         4  uart_driver.o(.text.my_printf)
    [Anonymous Symbol]                       0x000016d8   Section        0  pid.o(.text.pid_calculate_incremental)
    [Anonymous Symbol]                       0x00001774   Section        0  pid.o(.text.pid_calculate_positional)
    [Anonymous Symbol]                       0x000017fc   Section        0  pid.o(.text.pid_constrain)
    [Anonymous Symbol]                       0x00001826   Section        0  pid.o(.text.pid_init)
    [Anonymous Symbol]                       0x00001842   Section        0  pid.o(.text.pid_reset)
    [Anonymous Symbol]                       0x00001854   Section        0  pid.o(.text.pid_set_target)
    [Anonymous Symbol]                       0x00001858   Section        0  scheduler.o(.text.scheduler_init)
    [Anonymous Symbol]                       0x00001860   Section        0  scheduler.o(.text.scheduler_run)
    __arm_cp.2_0                             0x00001898   Number         4  scheduler.o(.text.scheduler_run)
    __arm_cp.2_1                             0x0000189c   Number         4  scheduler.o(.text.scheduler_run)
    [Anonymous Symbol]                       0x000018a0   Section        0  scheduler.o(.text.system_Task)
    [Anonymous Symbol]                       0x000018a4   Section        0  uart_driver.o(.text.uart0_task)
    __arm_cp.5_0                             0x000018e0   Number         4  uart_driver.o(.text.uart0_task)
    __arm_cp.5_1                             0x000018e4   Number         4  uart_driver.o(.text.uart0_task)
    __arm_cp.5_2                             0x000018e8   Number         4  uart_driver.o(.text.uart0_task)
    __arm_cp.5_3                             0x000018ec   Number         4  uart_driver.o(.text.uart0_task)
    __arm_cp.5_4                             0x000018f0   Number         4  uart_driver.o(.text.uart0_task)
    __arm_cp.5_5                             0x000018f4   Number         4  uart_driver.o(.text.uart0_task)
    i.__0vsnprintf                           0x00001914   Section        0  printfa.o(i.__0vsnprintf)
    i.__ARM_clz                              0x00001944   Section        0  depilogue.o(i.__ARM_clz)
    i.__scatterload_copy                     0x00001978   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x00001988   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x00001990   Section       14  handlers.o(i.__scatterload_zeroinit)
    _fp_digits                               0x000019a1   Thumb Code   344  printfa.o(i._fp_digits)
    i._fp_digits                             0x000019a0   Section        0  printfa.o(i._fp_digits)
    _printf_core                             0x00001b15   Thumb Code  1754  printfa.o(i._printf_core)
    i._printf_core                           0x00001b14   Section        0  printfa.o(i._printf_core)
    _printf_post_padding                     0x00002201   Thumb Code    32  printfa.o(i._printf_post_padding)
    i._printf_post_padding                   0x00002200   Section        0  printfa.o(i._printf_post_padding)
    _printf_pre_padding                      0x00002221   Thumb Code    44  printfa.o(i._printf_pre_padding)
    i._printf_pre_padding                    0x00002220   Section        0  printfa.o(i._printf_pre_padding)
    _snputc                                  0x0000224d   Thumb Code    22  printfa.o(i._snputc)
    i._snputc                                0x0000224c   Section        0  printfa.o(i._snputc)
    gMOTOR_PWMClockConfig                    0x00002262   Data           3  ti_msp_dl_config.o(.rodata.gMOTOR_PWMClockConfig)
    [Anonymous Symbol]                       0x00002262   Section        0  ti_msp_dl_config.o(.rodata.gMOTOR_PWMClockConfig)
    gMOTOR_PWMConfig                         0x00002268   Data           8  ti_msp_dl_config.o(.rodata.gMOTOR_PWMConfig)
    [Anonymous Symbol]                       0x00002268   Section        0  ti_msp_dl_config.o(.rodata.gMOTOR_PWMConfig)
    gSYSPLLConfig                            0x00002270   Data          40  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    [Anonymous Symbol]                       0x00002270   Section        0  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    gUART_0ClockConfig                       0x00002298   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x00002298   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x0000229a   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x0000229a   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    gUART_1ClockConfig                       0x000022a4   Data           2  ti_msp_dl_config.o(.rodata.gUART_1ClockConfig)
    [Anonymous Symbol]                       0x000022a4   Section        0  ti_msp_dl_config.o(.rodata.gUART_1ClockConfig)
    gUART_1Config                            0x000022a6   Data          10  ti_msp_dl_config.o(.rodata.gUART_1Config)
    [Anonymous Symbol]                       0x000022a6   Section        0  ti_msp_dl_config.o(.rodata.gUART_1Config)
    [Anonymous Symbol]                       0x000022b0   Section        0  uart_driver.o(.rodata.str1.1)
    scheduler_task                           0x2020005c   Data          72  scheduler.o(.data.scheduler_task)
    [Anonymous Symbol]                       0x2020005c   Section        0  scheduler.o(.data.scheduler_task)
    STACK                                    0x202002a0   Section     2048  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x000000c1   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x000000c5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x000000c9   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x000000c9   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x000000c9   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x000000c9   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x000000d1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x000000d1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x000000d5   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x000000d9   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x000000db   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x000000dd   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x000000df   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x000000e3   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART2_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART3_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __aeabi_memset                           0x000000e9   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x000000e9   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x000000e9   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x000000f7   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x000000f7   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x000000f7   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x000000fb   Thumb Code    18  memseta.o(.text)
    __aeabi_fadd                             0x0000010d   Thumb Code   162  fadd.o(.text)
    __aeabi_fsub                             0x000001af   Thumb Code     8  fadd.o(.text)
    __aeabi_frsub                            0x000001b7   Thumb Code     8  fadd.o(.text)
    __aeabi_fmul                             0x000001bf   Thumb Code   122  fmul.o(.text)
    __aeabi_fdiv                             0x00000239   Thumb Code   124  fdiv.o(.text)
    __aeabi_fcmplt                           0x000002b5   Thumb Code    28  fcmplt.o(.text)
    __aeabi_fcmpge                           0x000002d1   Thumb Code    28  fcmpge.o(.text)
    __aeabi_fcmpgt                           0x000002ed   Thumb Code    28  fcmpgt.o(.text)
    __aeabi_fcmpeq                           0x00000309   Thumb Code    28  fcmpeq.o(.text)
    __aeabi_i2f                              0x00000325   Thumb Code    22  fflti.o(.text)
    __aeabi_ui2f                             0x0000033b   Thumb Code    14  ffltui.o(.text)
    __aeabi_f2iz                             0x00000349   Thumb Code    50  ffixi.o(.text)
    __aeabi_f2uiz                            0x0000037b   Thumb Code    40  ffixui.o(.text)
    __aeabi_uidiv                            0x000003a3   Thumb Code     0  uidiv_div0.o(.text)
    __aeabi_uidivmod                         0x000003a3   Thumb Code    62  uidiv_div0.o(.text)
    __aeabi_uldivmod                         0x000003e1   Thumb Code    96  uldiv.o(.text)
    __I$use$fp                               0x00000441   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x00000441   Thumb Code    16  fepilogue.o(.text)
    _float_epilogue                          0x00000451   Thumb Code   114  fepilogue.o(.text)
    __aeabi_dadd                             0x000004c5   Thumb Code   328  dadd.o(.text)
    __aeabi_dsub                             0x0000060d   Thumb Code    12  dadd.o(.text)
    __aeabi_drsub                            0x00000619   Thumb Code    12  dadd.o(.text)
    __aeabi_dmul                             0x00000629   Thumb Code   202  dmul.o(.text)
    __aeabi_ddiv                             0x000006f9   Thumb Code   234  ddiv.o(.text)
    __aeabi_d2ulz                            0x000007e9   Thumb Code    54  dfixul.o(.text)
    __aeabi_cdrcmple                         0x00000829   Thumb Code    38  cdrcmple.o(.text)
    __scatterload                            0x00000851   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x00000851   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x00000881   Thumb Code    32  llshl.o(.text)
    _ll_shift_l                              0x00000881   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x000008a1   Thumb Code    34  llushr.o(.text)
    _ll_ushift_r                             0x000008a1   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x000008c3   Thumb Code    38  llsshr.o(.text)
    _ll_sshift_r                             0x000008c3   Thumb Code     0  llsshr.o(.text)
    _double_round                            0x000008e9   Thumb Code    26  depilogue.o(.text)
    _double_epilogue                         0x00000903   Thumb Code   164  depilogue.o(.text)
    DL_Common_delayCycles                    0x000009a7   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_SYSCTL_configSYSPLL                   0x000009b1   Thumb Code   196  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_setHFCLKSourceHFXTParams       0x00000a75   Thumb Code    80  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK    0x00000ac5   Thumb Code    40  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_Timer_initFourCCPWMMode               0x00000aed   Thumb Code   272  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    DL_Timer_setCaptCompUpdateMethod         0x00000bfd   Thumb Code    28  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    DL_Timer_setCaptureCompareOutCtl         0x00000c19   Thumb Code    24  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    DL_Timer_setCaptureCompareValue          0x00000c31   Thumb Code    16  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    DL_Timer_setClockConfig                  0x00000c41   Thumb Code    28  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_init                             0x00000c5d   Thumb Code    72  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x00000ca5   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    GROUP1_IRQHandler                        0x00000cb9   Thumb Code   156  encoder_driver.o(.text.GROUP1_IRQHandler)
    Gray_Task                                0x00000d65   Thumb Code   284  gray.o(.text.Gray_Task)
    Motor_Speed_l                            0x00000e9d   Thumb Code   116  motor_driver.o(.text.Motor_Speed_l)
    Motor_Speed_r                            0x00000f11   Thumb Code   124  motor_driver.o(.text.Motor_Speed_r)
    Motor_Stop                               0x00000f99   Thumb Code    52  motor_driver.o(.text.Motor_Stop)
    PID_Init                                 0x00000fd1   Thumb Code   100  pid_app.o(.text.PID_Init)
    PID_Task                                 0x00001035   Thumb Code   152  pid_app.o(.text.PID_Task)
    SYSCFG_DL_GPIO_init                      0x000010f9   Thumb Code   144  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_MOTOR_PWM_init                 0x000011bd   Thumb Code   116  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    SYSCFG_DL_SYSCTL_CLK_init                0x00001249   Thumb Code    16  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init)
    SYSCFG_DL_SYSCTL_init                    0x00001261   Thumb Code    96  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x000012cd   Thumb Code    36  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_UART_0_init                    0x000012fd   Thumb Code    88  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_UART_1_init                    0x00001365   Thumb Code    88  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    SYSCFG_DL_init                           0x000013d5   Thumb Code    36  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x000013f9   Thumb Code    44  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    SysTick_Handler                          0x00001441   Thumb Code    12  scheduler.o(.text.SysTick_Handler)
    UART0_IRQHandler                         0x0000144d   Thumb Code    48  uart_driver.o(.text.UART0_IRQHandler)
    UART1_IRQHandler                         0x00001491   Thumb Code    48  uart_driver.o(.text.UART1_IRQHandler)
    delay_ms                                 0x000014d9   Thumb Code    44  time.o(.text.delay_ms)
    encoder_config                           0x0000150d   Thumb Code    20  encoder_driver.o(.text.encoder_config)
    encoder_task                             0x00001521   Thumb Code   120  encoder_driver.o(.text.encoder_task)
    key_task                                 0x000015b1   Thumb Code   100  button_driver.o(.text.key_task)
    main                                     0x00001649   Thumb Code    52  main.o(.text.main)
    my_printf                                0x00001691   Thumb Code    68  uart_driver.o(.text.my_printf)
    pid_calculate_incremental                0x000016d9   Thumb Code   156  pid.o(.text.pid_calculate_incremental)
    pid_calculate_positional                 0x00001775   Thumb Code   136  pid.o(.text.pid_calculate_positional)
    pid_constrain                            0x000017fd   Thumb Code    42  pid.o(.text.pid_constrain)
    pid_init                                 0x00001827   Thumb Code    28  pid.o(.text.pid_init)
    pid_reset                                0x00001843   Thumb Code    18  pid.o(.text.pid_reset)
    pid_set_target                           0x00001855   Thumb Code     4  pid.o(.text.pid_set_target)
    scheduler_init                           0x00001859   Thumb Code     8  scheduler.o(.text.scheduler_init)
    scheduler_run                            0x00001861   Thumb Code    56  scheduler.o(.text.scheduler_run)
    system_Task                              0x000018a1   Thumb Code     2  scheduler.o(.text.system_Task)
    uart0_task                               0x000018a5   Thumb Code    60  uart_driver.o(.text.uart0_task)
    __0vsnprintf                             0x00001915   Thumb Code    44  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x00001915   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x00001915   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x00001915   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x00001915   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __ARM_clz                                0x00001945   Thumb Code    46  depilogue.o(i.__ARM_clz)
    __scatterload_copy                       0x00001979   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x00001989   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x00001991   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    Region$$Table$$Base                      0x000022b8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x000022d8   Number         0  anon$$obj.o(Region$$Table)
    basic_speed                              0x20200000   Data           4  pid_app.o(.data.basic_speed)
    gray_weights                             0x20200004   Data          28  gray.o(.data.gray_weights)
    pid_params_left                          0x20200020   Data          20  pid_app.o(.data.pid_params_left)
    pid_params_line                          0x20200034   Data          20  pid_app.o(.data.pid_params_line)
    pid_params_right                         0x20200048   Data          20  pid_app.o(.data.pid_params_right)
    Digtal                                   0x202000a8   Data           1  gray.o(.bss.Digtal)
    L_count                                  0x202000a9   Data           1  scheduler.o(.bss.L_count)
    baochi_flag                              0x202000aa   Data           1  gray.o(.bss.baochi_flag)
    encoder_get_count                        0x202000ac   Data           4  encoder_driver.o(.bss.encoder_get_count)
    encoder_left                             0x202000b0   Data          12  encoder_driver.o(.bss.encoder_left)
    encoder_right                            0x202000bc   Data          12  encoder_driver.o(.bss.encoder_right)
    g_line_position_error                    0x202000c8   Data           4  gray.o(.bss.g_line_position_error)
    key_down                                 0x202000cc   Data           1  button_driver.o(.bss.key_down)
    key_old                                  0x202000cd   Data           1  button_driver.o(.bss.key_old)
    key_up                                   0x202000ce   Data           1  button_driver.o(.bss.key_up)
    key_val                                  0x202000cf   Data           1  button_driver.o(.bss.key_val)
    led_state                                0x202000d0   Data           1  button_driver.o(.bss.led_state)
    pid_line_gray                            0x202000d4   Data          60  pid_app.o(.bss.pid_line_gray)
    pid_running                              0x20200110   Data           1  pid_app.o(.bss.pid_running)
    pid_speed_left                           0x20200114   Data          60  pid_app.o(.bss.pid_speed_left)
    pid_speed_right                          0x20200150   Data          60  pid_app.o(.bss.pid_speed_right)
    task_num                                 0x2020018c   Data           1  scheduler.o(.bss.task_num)
    uart_rx_buffer                           0x20200190   Data         128  uart_driver.o(.bss.uart_rx_buffer)
    uart_rx_buffer_1                         0x20200210   Data         128  uart_driver.o(.bss.uart_rx_buffer_1)
    uart_rx_index                            0x20200290   Data           1  uart_driver.o(.bss.uart_rx_index)
    uart_rx_index_1                          0x20200291   Data           1  uart_driver.o(.bss.uart_rx_index_1)
    uart_tick                                0x20200294   Data           4  uart_driver.o(.bss.uart_tick)
    uart_tick_1                              0x20200298   Data           4  uart_driver.o(.bss.uart_tick_1)
    uwTick                                   0x2020029c   Data           4  scheduler.o(.bss.uwTick)
    __initial_sp                             0x20200aa0   Data           0  startup_mspm0g350x_uvision.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x00002380, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x000022d8, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO            3    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000000   Code   RO          429  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x000000c0   0x000000c0   0x00000004   Code   RO          490    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x000000c4   0x000000c4   0x00000004   Code   RO          493    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO          495    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO          497    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x000000c8   0x000000c8   0x00000008   Code   RO          498    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO          500    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO          502    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x000000d0   0x000000d0   0x00000004   Code   RO          491    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x000000d4   0x000000d4   0x00000014   Code   RO            4    .text               startup_mspm0g350x_uvision.o
    0x000000e8   0x000000e8   0x00000024   Code   RO          438    .text               mc_p.l(memseta.o)
    0x0000010c   0x0000010c   0x000000b2   Code   RO          468    .text               mf_p.l(fadd.o)
    0x000001be   0x000001be   0x0000007a   Code   RO          470    .text               mf_p.l(fmul.o)
    0x00000238   0x00000238   0x0000007c   Code   RO          472    .text               mf_p.l(fdiv.o)
    0x000002b4   0x000002b4   0x0000001c   Code   RO          474    .text               mf_p.l(fcmplt.o)
    0x000002d0   0x000002d0   0x0000001c   Code   RO          476    .text               mf_p.l(fcmpge.o)
    0x000002ec   0x000002ec   0x0000001c   Code   RO          478    .text               mf_p.l(fcmpgt.o)
    0x00000308   0x00000308   0x0000001c   Code   RO          480    .text               mf_p.l(fcmpeq.o)
    0x00000324   0x00000324   0x00000016   Code   RO          482    .text               mf_p.l(fflti.o)
    0x0000033a   0x0000033a   0x0000000e   Code   RO          484    .text               mf_p.l(ffltui.o)
    0x00000348   0x00000348   0x00000032   Code   RO          486    .text               mf_p.l(ffixi.o)
    0x0000037a   0x0000037a   0x00000028   Code   RO          488    .text               mf_p.l(ffixui.o)
    0x000003a2   0x000003a2   0x0000003e   Code   RO          513    .text               mc_p.l(uidiv_div0.o)
    0x000003e0   0x000003e0   0x00000060   Code   RO          519    .text               mc_p.l(uldiv.o)
    0x00000440   0x00000440   0x00000000   Code   RO          521    .text               mc_p.l(iusefp.o)
    0x00000440   0x00000440   0x00000082   Code   RO          522    .text               mf_p.l(fepilogue.o)
    0x000004c2   0x000004c2   0x00000002   PAD
    0x000004c4   0x000004c4   0x00000164   Code   RO          524    .text               mf_p.l(dadd.o)
    0x00000628   0x00000628   0x000000d0   Code   RO          526    .text               mf_p.l(dmul.o)
    0x000006f8   0x000006f8   0x000000f0   Code   RO          528    .text               mf_p.l(ddiv.o)
    0x000007e8   0x000007e8   0x00000040   Code   RO          530    .text               mf_p.l(dfixul.o)
    0x00000828   0x00000828   0x00000028   Code   RO          532    .text               mf_p.l(cdrcmple.o)
    0x00000850   0x00000850   0x00000030   Code   RO          534    .text               mc_p.l(init.o)
    0x00000880   0x00000880   0x00000020   Code   RO          537    .text               mc_p.l(llshl.o)
    0x000008a0   0x000008a0   0x00000022   Code   RO          539    .text               mc_p.l(llushr.o)
    0x000008c2   0x000008c2   0x00000026   Code   RO          541    .text               mc_p.l(llsshr.o)
    0x000008e8   0x000008e8   0x000000be   Code   RO          544    .text               mf_p.l(depilogue.o)
    0x000009a6   0x000009a6   0x0000000a   Code   RO          248    .text.DL_Common_delayCycles  driverlib.a(dl_common.o)
    0x000009b0   0x000009b0   0x000000c4   Code   RO          398    .text.DL_SYSCTL_configSYSPLL  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00000a74   0x00000a74   0x00000050   Code   RO          412    .text.DL_SYSCTL_setHFCLKSourceHFXTParams  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00000ac4   0x00000ac4   0x00000028   Code   RO          406    .text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00000aec   0x00000aec   0x00000110   Code   RO          337    .text.DL_Timer_initFourCCPWMMode  driverlib.a(dl_timer.o)
    0x00000bfc   0x00000bfc   0x0000001c   Code   RO          303    .text.DL_Timer_setCaptCompUpdateMethod  driverlib.a(dl_timer.o)
    0x00000c18   0x00000c18   0x00000018   Code   RO          311    .text.DL_Timer_setCaptureCompareOutCtl  driverlib.a(dl_timer.o)
    0x00000c30   0x00000c30   0x00000010   Code   RO          263    .text.DL_Timer_setCaptureCompareValue  driverlib.a(dl_timer.o)
    0x00000c40   0x00000c40   0x0000001c   Code   RO          257    .text.DL_Timer_setClockConfig  driverlib.a(dl_timer.o)
    0x00000c5c   0x00000c5c   0x00000048   Code   RO          358    .text.DL_UART_init  driverlib.a(dl_uart.o)
    0x00000ca4   0x00000ca4   0x00000012   Code   RO          360    .text.DL_UART_setClockConfig  driverlib.a(dl_uart.o)
    0x00000cb6   0x00000cb6   0x00000002   PAD
    0x00000cb8   0x00000cb8   0x000000ac   Code   RO          103    .text.GROUP1_IRQHandler  encoder_driver.o
    0x00000d64   0x00000d64   0x00000138   Code   RO          179    .text.Gray_Task     gray.o
    0x00000e9c   0x00000e9c   0x00000074   Code   RO          124    .text.Motor_Speed_l  motor_driver.o
    0x00000f10   0x00000f10   0x00000088   Code   RO          126    .text.Motor_Speed_r  motor_driver.o
    0x00000f98   0x00000f98   0x00000038   Code   RO          128    .text.Motor_Stop    motor_driver.o
    0x00000fd0   0x00000fd0   0x00000064   Code   RO          226    .text.PID_Init      pid_app.o
    0x00001034   0x00001034   0x000000c4   Code   RO          230    .text.PID_Task      pid_app.o
    0x000010f8   0x000010f8   0x000000c4   Code   RO           15    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x000011bc   0x000011bc   0x0000008c   Code   RO           19    .text.SYSCFG_DL_MOTOR_PWM_init  ti_msp_dl_config.o
    0x00001248   0x00001248   0x00000018   Code   RO           27    .text.SYSCFG_DL_SYSCTL_CLK_init  ti_msp_dl_config.o
    0x00001260   0x00001260   0x0000006c   Code   RO           17    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x000012cc   0x000012cc   0x00000030   Code   RO           25    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x000012fc   0x000012fc   0x00000068   Code   RO           21    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x00001364   0x00001364   0x00000070   Code   RO           23    .text.SYSCFG_DL_UART_1_init  ti_msp_dl_config.o
    0x000013d4   0x000013d4   0x00000024   Code   RO           11    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x000013f8   0x000013f8   0x00000048   Code   RO           13    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x00001440   0x00001440   0x0000000c   Code   RO          199    .text.SysTick_Handler  scheduler.o
    0x0000144c   0x0000144c   0x00000044   Code   RO           62    .text.UART0_IRQHandler  uart_driver.o
    0x00001490   0x00001490   0x00000048   Code   RO           64    .text.UART1_IRQHandler  uart_driver.o
    0x000014d8   0x000014d8   0x00000034   Code   RO          167    .text.delay_ms      time.o
    0x0000150c   0x0000150c   0x00000014   Code   RO          111    .text.encoder_config  encoder_driver.o
    0x00001520   0x00001520   0x00000090   Code   RO          109    .text.encoder_task  encoder_driver.o
    0x000015b0   0x000015b0   0x00000098   Code   RO           87    .text.key_task      button_driver.o
    0x00001648   0x00001648   0x00000048   Code   RO           44    .text.main          main.o
    0x00001690   0x00001690   0x00000048   Code   RO           60    .text.my_printf     uart_driver.o
    0x000016d8   0x000016d8   0x0000009c   Code   RO          150    .text.pid_calculate_incremental  pid.o
    0x00001774   0x00001774   0x00000088   Code   RO          148    .text.pid_calculate_positional  pid.o
    0x000017fc   0x000017fc   0x0000002a   Code   RO          152    .text.pid_constrain  pid.o
    0x00001826   0x00001826   0x0000001c   Code   RO          138    .text.pid_init      pid.o
    0x00001842   0x00001842   0x00000012   Code   RO          146    .text.pid_reset     pid.o
    0x00001854   0x00001854   0x00000004   Code   RO          140    .text.pid_set_target  pid.o
    0x00001858   0x00001858   0x00000008   Code   RO          195    .text.scheduler_init  scheduler.o
    0x00001860   0x00001860   0x00000040   Code   RO          197    .text.scheduler_run  scheduler.o
    0x000018a0   0x000018a0   0x00000002   Code   RO          193    .text.system_Task   scheduler.o
    0x000018a2   0x000018a2   0x00000002   PAD
    0x000018a4   0x000018a4   0x00000070   Code   RO           66    .text.uart0_task    uart_driver.o
    0x00001914   0x00001914   0x00000030   Code   RO          446    i.__0vsnprintf      mc_p.l(printfa.o)
    0x00001944   0x00001944   0x0000002e   Code   RO          546    i.__ARM_clz         mf_p.l(depilogue.o)
    0x00001972   0x00001972   0x00000006   PAD
    0x00001978   0x00001978   0x0000000e   Code   RO          550    i.__scatterload_copy  mc_p.l(handlers.o)
    0x00001986   0x00001986   0x00000002   PAD
    0x00001988   0x00001988   0x00000002   Code   RO          551    i.__scatterload_null  mc_p.l(handlers.o)
    0x0000198a   0x0000198a   0x00000006   PAD
    0x00001990   0x00001990   0x0000000e   Code   RO          552    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x0000199e   0x0000199e   0x00000002   PAD
    0x000019a0   0x000019a0   0x00000174   Code   RO          448    i._fp_digits        mc_p.l(printfa.o)
    0x00001b14   0x00001b14   0x000006ec   Code   RO          449    i._printf_core      mc_p.l(printfa.o)
    0x00002200   0x00002200   0x00000020   Code   RO          450    i._printf_post_padding  mc_p.l(printfa.o)
    0x00002220   0x00002220   0x0000002c   Code   RO          451    i._printf_pre_padding  mc_p.l(printfa.o)
    0x0000224c   0x0000224c   0x00000016   Code   RO          452    i._snputc           mc_p.l(printfa.o)
    0x00002262   0x00002262   0x00000003   Data   RO           30    .rodata.gMOTOR_PWMClockConfig  ti_msp_dl_config.o
    0x00002265   0x00002265   0x00000003   PAD
    0x00002268   0x00002268   0x00000008   Data   RO           31    .rodata.gMOTOR_PWMConfig  ti_msp_dl_config.o
    0x00002270   0x00002270   0x00000028   Data   RO           29    .rodata.gSYSPLLConfig  ti_msp_dl_config.o
    0x00002298   0x00002298   0x00000002   Data   RO           32    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x0000229a   0x0000229a   0x0000000a   Data   RO           33    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x000022a4   0x000022a4   0x00000002   Data   RO           34    .rodata.gUART_1ClockConfig  ti_msp_dl_config.o
    0x000022a6   0x000022a6   0x0000000a   Data   RO           35    .rodata.gUART_1Config  ti_msp_dl_config.o
    0x000022b0   0x000022b0   0x00000005   Data   RO           76    .rodata.str1.1      uart_driver.o
    0x000022b5   0x000022b5   0x00000003   PAD
    0x000022b8   0x000022b8   0x00000020   Data   RO          549    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x000022d8, Size: 0x00000aa0, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   0x000022d8   0x00000004   Data   RW          232    .data.basic_speed   pid_app.o
    0x20200004   0x000022dc   0x0000001c   Data   RW          181    .data.gray_weights  gray.o
    0x20200020   0x000022f8   0x00000014   Data   RW          233    .data.pid_params_left  pid_app.o
    0x20200034   0x0000230c   0x00000014   Data   RW          235    .data.pid_params_line  pid_app.o
    0x20200048   0x00002320   0x00000014   Data   RW          234    .data.pid_params_right  pid_app.o
    0x2020005c   0x00002334   0x00000048   Data   RW          206    .data.scheduler_task  scheduler.o
    0x202000a4   0x0000237c   0x00000004   PAD
    0x202000a8        -       0x00000001   Zero   RW          183    .bss.Digtal         gray.o
    0x202000a9        -       0x00000001   Zero   RW          203    .bss.L_count        scheduler.o
    0x202000aa        -       0x00000001   Zero   RW          182    .bss.baochi_flag    gray.o
    0x202000ab   0x0000237c   0x00000001   PAD
    0x202000ac        -       0x00000004   Zero   RW          113    .bss.encoder_get_count  encoder_driver.o
    0x202000b0        -       0x0000000c   Zero   RW          114    .bss.encoder_left   encoder_driver.o
    0x202000bc        -       0x0000000c   Zero   RW          115    .bss.encoder_right  encoder_driver.o
    0x202000c8        -       0x00000004   Zero   RW          184    .bss.g_line_position_error  gray.o
    0x202000cc        -       0x00000001   Zero   RW           91    .bss.key_down       button_driver.o
    0x202000cd        -       0x00000001   Zero   RW           90    .bss.key_old        button_driver.o
    0x202000ce        -       0x00000001   Zero   RW           92    .bss.key_up         button_driver.o
    0x202000cf        -       0x00000001   Zero   RW           89    .bss.key_val        button_driver.o
    0x202000d0        -       0x00000001   Zero   RW           93    .bss.led_state      button_driver.o
    0x202000d1   0x0000237c   0x00000003   PAD
    0x202000d4        -       0x0000003c   Zero   RW          238    .bss.pid_line_gray  pid_app.o
    0x20200110        -       0x00000001   Zero   RW          239    .bss.pid_running    pid_app.o
    0x20200111   0x0000237c   0x00000003   PAD
    0x20200114        -       0x0000003c   Zero   RW          236    .bss.pid_speed_left  pid_app.o
    0x20200150        -       0x0000003c   Zero   RW          237    .bss.pid_speed_right  pid_app.o
    0x2020018c        -       0x00000001   Zero   RW          204    .bss.task_num       scheduler.o
    0x2020018d   0x0000237c   0x00000003   PAD
    0x20200190        -       0x00000080   Zero   RW           70    .bss.uart_rx_buffer  uart_driver.o
    0x20200210        -       0x00000080   Zero   RW           73    .bss.uart_rx_buffer_1  uart_driver.o
    0x20200290        -       0x00000001   Zero   RW           71    .bss.uart_rx_index  uart_driver.o
    0x20200291        -       0x00000001   Zero   RW           74    .bss.uart_rx_index_1  uart_driver.o
    0x20200292   0x0000237c   0x00000002   PAD
    0x20200294        -       0x00000004   Zero   RW           72    .bss.uart_tick      uart_driver.o
    0x20200298        -       0x00000004   Zero   RW           75    .bss.uart_tick_1    uart_driver.o
    0x2020029c        -       0x00000004   Zero   RW          205    .bss.uwTick         scheduler.o
    0x202002a0        -       0x00000800   Zero   RW            1    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       152         52          0          0          5       5762   button_driver.o
       336         40          0          0         28       6724   encoder_driver.o
       312         28          0         28          6       5059   gray.o
        72         20          0          0          0       4338   main.o
       308         16          0          0          0       6736   motor_driver.o
       384          0          0          0          0       3080   pid.o
       296         44          0         64        181       1863   pid_app.o
        86          8          0         72          6       1568   scheduler.o
        20          4        192          0       2048        612   startup_mspm0g350x_uvision.o
       840        176         75          0          0      25370   ti_msp_dl_config.o
        52          8          0          0          0       1885   time.o
       324        100          5          0        266       5950   uart_driver.o

    ----------------------------------------------------------------------
      3184        <USER>        <GROUP>        164       2556      68947   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          6          0         16          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        10          0          0          0          0        803   dl_common.o
       316         28          0          0          0      12876   dl_sysctl_mspm0g1x0x_g3x0x.o
       368         44          0          0          0      41556   dl_timer.o
        90          8          0          0          0      14163   dl_uart.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        48         10          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        32          0          0          0          0         68   llshl.o
        38          0          0          0          0         68   llsshr.o
        34          0          0          0          0         68   llushr.o
        36          0          0          0          0        100   memseta.o
      2290         94          0          0          0        460   printfa.o
        62          0          0          0          0         72   uidiv_div0.o
        96          0          0          0          0         84   uldiv.o
        40          2          0          0          0         68   cdrcmple.o
       356          4          0          0          0        140   dadd.o
       240          6          0          0          0         84   ddiv.o
       236          0          0          0          0        216   depilogue.o
        64         10          0          0          0         68   dfixul.o
       208          6          0          0          0         88   dmul.o
       178          0          0          0          0        108   fadd.o
        28          0          0          0          0         60   fcmpeq.o
        28          0          0          0          0         60   fcmpge.o
        28          0          0          0          0         60   fcmpgt.o
        28          0          0          0          0         60   fcmplt.o
       124          0          0          0          0         72   fdiv.o
       130          0          0          0          0        144   fepilogue.o
        50          0          0          0          0         60   ffixi.o
        40          0          0          0          0         60   ffixui.o
        22          0          0          0          0         68   fflti.o
        14          0          0          0          0         68   ffltui.o
       122          0          0          0          0         72   fmul.o

    ----------------------------------------------------------------------
      5426        <USER>          <GROUP>          0          0      71942   Library Totals
        20          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       784         80          0          0          0      69398   driverlib.a
      2686        112          0          0          0        988   mc_p.l
      1936         28          0          0          0       1556   mf_p.l

    ----------------------------------------------------------------------
      5426        <USER>          <GROUP>          0          0      71942   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      8610        716        310        164       2556     139477   Grand Totals
      8610        716        310        164       2556     139477   ELF Image Totals
      8610        716        310        164          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 8920 (   8.71kB)
    Total RW  Size (RW Data + ZI Data)              2720 (   2.66kB)
    Total ROM Size (Code + RO Data + RW Data)       9084 (   8.87kB)

==============================================================================

