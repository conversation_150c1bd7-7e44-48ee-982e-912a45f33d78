/**
 * @file gray_12_test.c
 * @brief 12路灰度传感器权重矩阵测试程序
 * @date 2025-01-02
 * <AUTHOR> (工程师)
 */

#include "gray.h"

/**
 * @brief 测试12路权重矩阵的对称性
 */
void test_weight_symmetry_12(void)
{
    // 验证12路权重数组的对称性
    // gray_weights_12[12] = {-11, -9, -7, -5, -3, -1, 1, 3, 5, 7, 9, 11}
    //                        [0] [1] [2] [3] [4] [5] [6] [7] [8] [9][10][11]
    
    bool symmetry_ok = true;
    
    // 检查对称性: weights[i] + weights[11-i] = 0
    for(int i = 0; i < 6; i++) {
        if(gray_weights_12[i] + gray_weights_12[11-i] != 0.0f) {
            symmetry_ok = false;
            break;
        }
    }
    
    // 可以通过串口输出验证结果
    // my_printf(UART_0_INST, "12路权重对称性: %s\r\n", symmetry_ok ? "PASS" : "FAIL");
}

/**
 * @brief 测试12路数据映射和权重计算
 * @param raw_12bit 12路原始数据
 * @return 计算得到的偏差值
 */
float test_12bit_weight_calculation(uint16_t raw_12bit)
{
    // 模拟Gray_Task()的权重计算逻辑
    uint16_t digital_test = ~raw_12bit;  // 模拟取反操作
    
    float weighted_sum = 0;
    uint8_t black_line_count = 0;
    
    for(uint8_t i = 0; i < 12; i++)
    {
        if((digital_test >> i) & 0x01)
        {
            weighted_sum += gray_weights_12[i];
            black_line_count++;
        }
    }
    
    float position_error = 0;
    if(black_line_count > 0)
        position_error = weighted_sum / (float)black_line_count;
    
    return position_error;
}

/**
 * @brief 测试左转检测逻辑
 * @param raw_12bit 12路原始数据
 * @return 是否检测到左转条件
 */
bool test_left_turn_detection_12(uint16_t raw_12bit)
{
    // 模拟数据取反
    uint16_t digital_test = ~raw_12bit;
    
    // 测试左转检测逻辑：bit9、bit10、bit11同时为1
    return ((digital_test>>9)&0x01) && ((digital_test>>10)&0x01) && ((digital_test>>11)&0x01);
}

/**
 * @brief 运行12路灰度传感器测试用例
 */
void run_12bit_gray_tests(void)
{
    // 测试用例1: 中心线检测
    // 12路数据: bit5和bit6为1 (中心位置)
    uint16_t test_center = 0b000001100000;  // 0x0060
    float error_center = test_12bit_weight_calculation(test_center);
    // 预期结果: 权重-1.0和1.0的平均值 = 0.0
    
    // 测试用例2: 左偏检测
    // 12路数据: bit2和bit3为1 (左侧位置)
    uint16_t test_left = 0b000000001100;   // 0x000C
    float error_left = test_12bit_weight_calculation(test_left);
    // 预期结果: 权重-7.0和-5.0的平均值 = -6.0
    
    // 测试用例3: 右偏检测
    // 12路数据: bit8和bit9为1 (右侧位置)
    uint16_t test_right = 0b001100000000;  // 0x0300
    float error_right = test_12bit_weight_calculation(test_right);
    // 预期结果: 权重5.0和7.0的平均值 = 6.0
    
    // 测试用例4: 左转检测
    // 12路数据: bit9、bit10、bit11为1 (右侧3路)
    uint16_t test_left_turn = 0b111000000000;  // 0x0E00
    bool is_left_turn = test_left_turn_detection_12(test_left_turn);
    // 预期结果: true
    
    // 测试用例5: 边缘检测
    // 12路数据: bit0和bit11为1 (两端边缘)
    uint16_t test_edge = 0b100000000001;  // 0x0801
    float error_edge = test_12bit_weight_calculation(test_edge);
    // 预期结果: 权重-11.0和11.0的平均值 = 0.0
    
    // 测试用例6: 宽线检测
    // 12路数据: bit4到bit7为1 (中间4路)
    uint16_t test_wide = 0b000011110000;  // 0x00F0
    float error_wide = test_12bit_weight_calculation(test_wide);
    // 预期结果: 权重(-3,-1,1,3)的平均值 = 0.0
    
    // 可以通过串口输出测试结果进行验证
    // my_printf(UART_0_INST, "中心线偏差: %.2f (预期: 0.0)\r\n", error_center);
    // my_printf(UART_0_INST, "左偏偏差: %.2f (预期: -6.0)\r\n", error_left);
    // my_printf(UART_0_INST, "右偏偏差: %.2f (预期: 6.0)\r\n", error_right);
    // my_printf(UART_0_INST, "左转检测: %d (预期: 1)\r\n", is_left_turn);
    // my_printf(UART_0_INST, "边缘偏差: %.2f (预期: 0.0)\r\n", error_edge);
    // my_printf(UART_0_INST, "宽线偏差: %.2f (预期: 0.0)\r\n", error_wide);
}

/**
 * @brief 验证权重分布的合理性
 */
void verify_weight_distribution_12(void)
{
    // 验证权重分布特性
    
    // 1. 对称性检查
    bool symmetric = true;
    for(int i = 0; i < 6; i++) {
        if(gray_weights_12[i] != -gray_weights_12[11-i]) {
            symmetric = false;
            break;
        }
    }
    
    // 2. 单调性检查 (从左到右应该单调递增)
    bool monotonic = true;
    for(int i = 0; i < 11; i++) {
        if(gray_weights_12[i] >= gray_weights_12[i+1]) {
            monotonic = false;
            break;
        }
    }
    
    // 3. 边缘权重检查 (边缘权重应该最大)
    bool edge_max = (gray_weights_12[0] == -11.0f) && (gray_weights_12[11] == 11.0f);
    
    // 4. 中心权重检查 (中心权重应该接近0)
    bool center_near_zero = (gray_weights_12[5] == -1.0f) && (gray_weights_12[6] == 1.0f);
    
    // 可以通过串口输出验证结果
    // my_printf(UART_0_INST, "权重对称性: %s\r\n", symmetric ? "PASS" : "FAIL");
    // my_printf(UART_0_INST, "权重单调性: %s\r\n", monotonic ? "PASS" : "FAIL");
    // my_printf(UART_0_INST, "边缘权重: %s\r\n", edge_max ? "PASS" : "FAIL");
    // my_printf(UART_0_INST, "中心权重: %s\r\n", center_near_zero ? "PASS" : "FAIL");
}

/**
 * @brief 性能对比测试
 */
void performance_comparison_test(void)
{
    // 对比12路方案与原7路方案的理论性能
    
    // 原7路权重范围: [-6, 6], 总范围 = 12
    // 新12路权重范围: [-11, 11], 总范围 = 22
    // 理论精度提升: 22/12 ≈ 1.83倍
    
    float range_7bit = 6.0f - (-6.0f);      // 12.0
    float range_12bit = 11.0f - (-11.0f);   // 22.0
    float precision_improvement = range_12bit / range_7bit;  // 1.83
    
    // 分辨率对比
    float resolution_7bit = range_7bit / 7;    // 1.71 per sensor
    float resolution_12bit = range_12bit / 12; // 1.83 per sensor
    
    // 可以通过串口输出对比结果
    // my_printf(UART_0_INST, "精度提升倍数: %.2f\r\n", precision_improvement);
    // my_printf(UART_0_INST, "7路分辨率: %.2f\r\n", resolution_7bit);
    // my_printf(UART_0_INST, "12路分辨率: %.2f\r\n", resolution_12bit);
}

/**
 * @brief 主测试函数
 */
void run_all_12bit_tests(void)
{
    test_weight_symmetry_12();
    run_12bit_gray_tests();
    verify_weight_distribution_12();
    performance_comparison_test();
}

// 测试完成标志
volatile bool test_12bit_completed = false;

/**
 * @brief 测试完成回调
 */
void test_12bit_completion_callback(void)
{
    test_12bit_completed = true;
}
