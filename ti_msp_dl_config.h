/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3507

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)


#define GPIO_HFXT_PORT                                                     GPIOA
#define GPIO_HFXIN_PIN                                             DL_GPIO_PIN_5
#define GPIO_HFXIN_IOMUX                                         (IOMUX_PINCM10)
#define GPIO_HFXOUT_PIN                                            DL_GPIO_PIN_6
#define GPIO_HFXOUT_IOMUX                                        (IOMUX_PINCM11)
#define CPUCLK_FREQ                                                     80000000



/* Defines for MOTOR_PWM */
#define MOTOR_PWM_INST                                                     TIMG0
#define MOTOR_PWM_INST_IRQHandler                               TIMG0_IRQHandler
#define MOTOR_PWM_INST_INT_IRQN                                 (TIMG0_INT_IRQn)
#define MOTOR_PWM_INST_CLK_FREQ                                         20000000
/* GPIO defines for channel 0 */
#define GPIO_MOTOR_PWM_C0_PORT                                             GPIOB
#define GPIO_MOTOR_PWM_C0_PIN                                     DL_GPIO_PIN_10
#define GPIO_MOTOR_PWM_C0_IOMUX                                  (IOMUX_PINCM27)
#define GPIO_MOTOR_PWM_C0_IOMUX_FUNC                 IOMUX_PINCM27_PF_TIMG0_CCP0
#define GPIO_MOTOR_PWM_C0_IDX                                DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_MOTOR_PWM_C1_PORT                                             GPIOB
#define GPIO_MOTOR_PWM_C1_PIN                                     DL_GPIO_PIN_11
#define GPIO_MOTOR_PWM_C1_IOMUX                                  (IOMUX_PINCM28)
#define GPIO_MOTOR_PWM_C1_IOMUX_FUNC                 IOMUX_PINCM28_PF_TIMG0_CCP1
#define GPIO_MOTOR_PWM_C1_IDX                                DL_TIMER_CC_1_INDEX



/* Defines for UART_0 */
#define UART_0_INST                                                        UART0
#define UART_0_INST_FREQUENCY                                            4000000
#define UART_0_INST_IRQHandler                                  UART0_IRQHandler
#define UART_0_INST_INT_IRQN                                      UART0_INT_IRQn
#define GPIO_UART_0_RX_PORT                                                GPIOA
#define GPIO_UART_0_TX_PORT                                                GPIOA
#define GPIO_UART_0_RX_PIN                                        DL_GPIO_PIN_11
#define GPIO_UART_0_TX_PIN                                        DL_GPIO_PIN_10
#define GPIO_UART_0_IOMUX_RX                                     (IOMUX_PINCM22)
#define GPIO_UART_0_IOMUX_TX                                     (IOMUX_PINCM21)
#define GPIO_UART_0_IOMUX_RX_FUNC                      IOMUX_PINCM22_PF_UART0_RX
#define GPIO_UART_0_IOMUX_TX_FUNC                      IOMUX_PINCM21_PF_UART0_TX
#define UART_0_BAUD_RATE                                                (115200)
#define UART_0_IBRD_4_MHZ_115200_BAUD                                        (2)
#define UART_0_FBRD_4_MHZ_115200_BAUD                                       (11)
/* Defines for UART_1 */
#define UART_1_INST                                                        UART1
#define UART_1_INST_FREQUENCY                                            4000000
#define UART_1_INST_IRQHandler                                  UART1_IRQHandler
#define UART_1_INST_INT_IRQN                                      UART1_INT_IRQn
#define GPIO_UART_1_RX_PORT                                                GPIOA
#define GPIO_UART_1_TX_PORT                                                GPIOA
#define GPIO_UART_1_RX_PIN                                         DL_GPIO_PIN_9
#define GPIO_UART_1_TX_PIN                                         DL_GPIO_PIN_8
#define GPIO_UART_1_IOMUX_RX                                     (IOMUX_PINCM20)
#define GPIO_UART_1_IOMUX_TX                                     (IOMUX_PINCM19)
#define GPIO_UART_1_IOMUX_RX_FUNC                      IOMUX_PINCM20_PF_UART1_RX
#define GPIO_UART_1_IOMUX_TX_FUNC                      IOMUX_PINCM19_PF_UART1_TX
#define UART_1_BAUD_RATE                                                (115200)
#define UART_1_IBRD_4_MHZ_115200_BAUD                                        (2)
#define UART_1_FBRD_4_MHZ_115200_BAUD                                       (11)





/* Port definition for Pin Group LED1 */
#define LED1_PORT                                                        (GPIOB)

/* Defines for PIN_22: GPIOB.22 with pinCMx 50 on package pin 21 */
#define LED1_PIN_22_PIN                                         (DL_GPIO_PIN_22)
#define LED1_PIN_22_IOMUX                                        (IOMUX_PINCM50)
/* Port definition for Pin Group KEY */
#define KEY_PORT                                                         (GPIOB)

/* Defines for PIN_21: GPIOB.21 with pinCMx 49 on package pin 20 */
#define KEY_PIN_21_PIN                                          (DL_GPIO_PIN_21)
#define KEY_PIN_21_IOMUX                                         (IOMUX_PINCM49)
/* Port definition for Pin Group BNO_RST */
#define BNO_RST_PORT                                                     (GPIOB)

/* Defines for PIN_4: GPIOB.14 with pinCMx 31 on package pin 2 */
#define BNO_RST_PIN_4_PIN                                       (DL_GPIO_PIN_14)
#define BNO_RST_PIN_4_IOMUX                                      (IOMUX_PINCM31)
/* Port definition for Pin Group MOTOR_EN */
#define MOTOR_EN_PORT                                                    (GPIOB)

/* Defines for STBY: GPIOB.12 with pinCMx 29 on package pin 64 */
#define MOTOR_EN_STBY_PIN                                       (DL_GPIO_PIN_12)
#define MOTOR_EN_STBY_IOMUX                                      (IOMUX_PINCM29)
/* Port definition for Pin Group ENCODER */
#define ENCODER_PORT                                                     (GPIOB)

/* Defines for left_a: GPIOB.6 with pinCMx 23 on package pin 58 */
// pins affected by this interrupt request:["left_a","left_b","right_a","right_b"]
#define ENCODER_INT_IRQN                                        (GPIOB_INT_IRQn)
#define ENCODER_INT_IIDX                        (DL_INTERRUPT_GROUP1_IIDX_GPIOB)
#define ENCODER_left_a_IIDX                                  (DL_GPIO_IIDX_DIO6)
#define ENCODER_left_a_PIN                                       (DL_GPIO_PIN_6)
#define ENCODER_left_a_IOMUX                                     (IOMUX_PINCM23)
/* Defines for left_b: GPIOB.7 with pinCMx 24 on package pin 59 */
#define ENCODER_left_b_IIDX                                  (DL_GPIO_IIDX_DIO7)
#define ENCODER_left_b_PIN                                       (DL_GPIO_PIN_7)
#define ENCODER_left_b_IOMUX                                     (IOMUX_PINCM24)
/* Defines for right_a: GPIOB.8 with pinCMx 25 on package pin 60 */
#define ENCODER_right_a_IIDX                                 (DL_GPIO_IIDX_DIO8)
#define ENCODER_right_a_PIN                                      (DL_GPIO_PIN_8)
#define ENCODER_right_a_IOMUX                                    (IOMUX_PINCM25)
/* Defines for right_b: GPIOB.9 with pinCMx 26 on package pin 61 */
#define ENCODER_right_b_IIDX                                 (DL_GPIO_IIDX_DIO9)
#define ENCODER_right_b_PIN                                      (DL_GPIO_PIN_9)
#define ENCODER_right_b_IOMUX                                    (IOMUX_PINCM26)
/* Port definition for Pin Group MOTOR_L */
#define MOTOR_L_PORT                                                     (GPIOB)

/* Defines for AIN1: GPIOB.0 with pinCMx 12 on package pin 47 */
#define MOTOR_L_AIN1_PIN                                         (DL_GPIO_PIN_0)
#define MOTOR_L_AIN1_IOMUX                                       (IOMUX_PINCM12)
/* Defines for AIN2: GPIOB.1 with pinCMx 13 on package pin 48 */
#define MOTOR_L_AIN2_PIN                                         (DL_GPIO_PIN_1)
#define MOTOR_L_AIN2_IOMUX                                       (IOMUX_PINCM13)
/* Port definition for Pin Group MOTOR_R */
#define MOTOR_R_PORT                                                     (GPIOB)

/* Defines for BIN1: GPIOB.4 with pinCMx 17 on package pin 52 */
#define MOTOR_R_BIN1_PIN                                         (DL_GPIO_PIN_4)
#define MOTOR_R_BIN1_IOMUX                                       (IOMUX_PINCM17)
/* Defines for BIN2: GPIOB.5 with pinCMx 18 on package pin 53 */
#define MOTOR_R_BIN2_PIN                                         (DL_GPIO_PIN_5)
#define MOTOR_R_BIN2_IOMUX                                       (IOMUX_PINCM18)
/* Port definition for Pin Group GRAY */
#define GRAY_PORT                                                        (GPIOB)

/* Defines for G_0: GPIOB.17 with pinCMx 43 on package pin 14 */
#define GRAY_G_0_PIN                                            (DL_GPIO_PIN_17)
#define GRAY_G_0_IOMUX                                           (IOMUX_PINCM43)
/* Defines for G_1: GPIOB.18 with pinCMx 44 on package pin 15 */
#define GRAY_G_1_PIN                                            (DL_GPIO_PIN_18)
#define GRAY_G_1_IOMUX                                           (IOMUX_PINCM44)
/* Defines for G_2: GPIOB.19 with pinCMx 45 on package pin 16 */
#define GRAY_G_2_PIN                                            (DL_GPIO_PIN_19)
#define GRAY_G_2_IOMUX                                           (IOMUX_PINCM45)
/* Defines for G_3: GPIOB.20 with pinCMx 48 on package pin 19 */
#define GRAY_G_3_PIN                                            (DL_GPIO_PIN_20)
#define GRAY_G_3_IOMUX                                           (IOMUX_PINCM48)
/* Defines for G_4: GPIOB.24 with pinCMx 52 on package pin 23 */
#define GRAY_G_4_PIN                                            (DL_GPIO_PIN_24)
#define GRAY_G_4_IOMUX                                           (IOMUX_PINCM52)
/* Defines for G_5: GPIOB.25 with pinCMx 56 on package pin 27 */
#define GRAY_G_5_PIN                                            (DL_GPIO_PIN_25)
#define GRAY_G_5_IOMUX                                           (IOMUX_PINCM56)
/* Defines for G_6: GPIOB.2 with pinCMx 15 on package pin 50 */
#define GRAY_G_6_PIN                                             (DL_GPIO_PIN_2)
#define GRAY_G_6_IOMUX                                           (IOMUX_PINCM15)
/* Port definition for Pin Group I2C_PINS */
#define I2C_PINS_PORT                                                    (GPIOA)

/* Defines for SCL: GPIOA.0 with pinCMx 1 on package pin 33 */
#define I2C_PINS_SCL_PIN                                         (DL_GPIO_PIN_0)
#define I2C_PINS_SCL_IOMUX                                        (IOMUX_PINCM1)
/* Defines for SDA: GPIOA.1 with pinCMx 2 on package pin 34 */
#define I2C_PINS_SDA_PIN                                         (DL_GPIO_PIN_1)
#define I2C_PINS_SDA_IOMUX                                        (IOMUX_PINCM2)



/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_SYSCTL_CLK_init(void);
void SYSCFG_DL_MOTOR_PWM_init(void);
void SYSCFG_DL_UART_0_init(void);
void SYSCFG_DL_UART_1_init(void);

void SYSCFG_DL_SYSTICK_init(void);

bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
