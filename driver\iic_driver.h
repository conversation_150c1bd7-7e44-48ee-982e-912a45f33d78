//#ifndef __IIC_DRIVER_H
//#define __IIC_DRIVER_H

//#include "bsp_system.h"

///**
// * @brief IIC单字节数据写入
// * 
// * @param i2c                   iic句柄
// * 
// * @param I2C_TARGET_ADDRESS    目标设备地址
// * 
// * @param addr                  寄存器地址
// * 
// * @param data                  数据
// * 
// * @return int 
// */
//int I2C_WriteByte(I2C_Regs *i2c,uint32_t I2C_TARGET_ADDRESS,uint8_t addr,uint8_t data);
///**
// * @brief IIC写多个字节数据
// * 
// * @param i2c                   iic句柄
// *
// * @param I2C_TARGET_ADDRESS    目标设备地址
// *
// * @param data                  数据
// *
// * @param length								数据长度
// *
// * @return int 
// */
//int I2C_WriteMultiBytes(I2C_Regs *i2c, uint32_t I2C_TARGET_ADDRESS, uint8_t *data, uint8_t length);
///**
// * @brief IIC多字节数据读取
// * 
// * @param i2c        IIC句柄
// * 
// * @param slave_addr 设备地址
// * 
// * @param reg_addr   寄存器地址
// * 
// * @param length     读取长度
// * 
// * @param data       数据存储地址
// * 
// * @return int 
// */
//int IIC_Read(I2C_Regs *i2c,unsigned char slave_addr,unsigned char reg_addr,unsigned char length,unsigned char *data);

//void iic_test(void);

//#endif